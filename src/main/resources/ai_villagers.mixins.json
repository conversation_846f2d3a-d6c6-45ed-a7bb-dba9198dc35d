{"required": true, "package": "AiVillagers.mixins", "compatibilityLevel": "JAVA_22", "mixins": ["accessors.ButcherAccessor", "accessors.GlobalAccessor", "accessors.StructureAccessorAccess", "behavior.EntityPathBehaviorMixin", "behavior.IronGolemAttackRangeMixin", "behavior.IronGolemMultiAttackMixin", "behavior.VillagerDoorInteractionMixin", "behavior.VillagerGenderBreedingMixin", "behavior.VillagerGenderSystemMixin", "behavior.VillagerItemCollectionMixin", "behavior.VillagerWorkTaskDisableMixin", "core.ExperienceOrbTrackingMixin", "core.VillagerDimensionOptimizationMixin", "health.VillagerDamageTaskInterruptMixin", "health.VillagerHealthRegenerationMixin", "professions.butcher.ButcherBehaviorM<PERSON>in", "professions.farmer.Farmer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "professions.fisher.FisherBehaviorMixin", "professions.fisher.VillagerFishingBobberTrackingMixin", "professions.nitwit.NitwitBehaviorMixin", "protection.FarmlandVillagerProtectionMixin", "protection.VillagerBedProtectionMixin", "world.OutpostSpawnRestrictionMixin", "world.PointOfInterestSearchRadiusMixin", "world.VillageAnimalSpawnMarkingMixin", "world.VillageAnimalSystemMixin", "world.VillagerExperienceAttractionMixin", "world.animals.CowVillageStatusInheritanceMixin", "world.animals.PigVillageStatusInheritanceMixin", "world.animals.SheepVillageStatusInheritanceMixin"], "injectors": {"defaultRequire": 1}}