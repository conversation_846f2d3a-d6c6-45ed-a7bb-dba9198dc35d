package AiVillagers.interfaces;

import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.item.Item;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.BlockPos;
import net.minecraft.village.VillagerData;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ButcherHelper {
    public AnimalEntity target;
    public int lookTimer;

    public ButcherHelper(AnimalEntity target) {
        this.target = target;
        this.lookTimer = 0;
    }

    public record FeedingTick(String uuid, long lastFeedTick) {}

    public static final Codec<FeedingTick> FEEDING_TICK_CODEC = RecordCodecBuilder.create(instance ->
            instance.group(
                    Codec.STRING.fieldOf("uuid").forGetter(FeedingTick::uuid),
                    Codec.LONG.fieldOf("lastFeedTick").forGetter(FeedingTick::lastFeedTick)
            ).apply(instance, FeedingTick::new)
    );

    public interface ButcherAccess {
        Map<UUID, Long> aiVillagersFabric$getLastFeedingTimestamps();
        List<AnimalEntity> aiVillagersFabric$getFedAnimals();
        ButcherState aiVillagersFabric$getState(); // Añadido
    }

    public static class ButcherState {
        public enum Phase {
            IDLE, FEED, BUTCHER, DEPOSIT, COOK
        }

        public int freeze = 0;
        public int postKill = 0;
        public int cleanup = 0;
        public int butcherTickCounter = 0;
        public int meatToCookCount = 0;
        public int feedingObservationTimer = 0;
        public int feedingDelay = 0;
        public int chestDepositDelay = 0;
        public int feedingStep = 0;
        public int groupButcheringDelay = 0;
        public int chestAnimationTimer = 0;
        public int routinePhase = 0;
        public int chestSearchCooldown = 0;
        public int professionLossTimer = 0;


        public static final Set<String> BIOMES = Set.of("plains", "desert", "savanna", "taiga", "snowy", "jungle");
        public static final Set<Identifier> ANIMAL_PENS = BIOMES.stream().flatMap(biome -> Stream.of(
                Identifier.of("minecraft:village/" + biome + "/houses/" + biome + "_animal_pen_1"),
                Identifier.of("minecraft:village/" + biome + "/houses/" + biome + "_animal_pen_2"),
                Identifier.of("minecraft:village/" + biome + "/houses/" + biome + "_animal_pen_3"))).collect(Collectors.toSet());
        public static final Set<Identifier> STABLES = BIOMES.stream().flatMap(biome -> Stream.of(
                Identifier.of("minecraft:village/" + biome + "/houses/" + biome + "_stable_1"),
                Identifier.of("minecraft:village/" + biome + "/houses/" + biome + "_stable_2"))).collect(Collectors.toSet());
        public static final String NBT_KEY_PEN = "AnimalPenPos";
        public static final String NBT_KEY_STABLE = "StablePos";

        public VillagerData previousData;

        public Phase phase = Phase.IDLE;

        public boolean firstItemCycle = true;

        public boolean isFirstItemCycle() {
            return firstItemCycle;
        }

        public void setFirstItemCycle(boolean value) {
            this.firstItemCycle = value;
        }

        public int flags = 0;
        public static final int FLAG_HAS_BONUS = 1;
        public static final int FLAG_CHEST_OPEN = 1 << 1;
        public static final int FLAG_DEPOSITING = 1 << 2;
        public static final int FLAG_DEPOSIT_COMPLETED = 1 << 3;
        public static final int FLAG_WAS_BUTCHER_ITEM = 1 << 4;
        public static final int FLAG_STRUCTURES_ASSIGNED = 1 << 5;
        public static final int FLAG_ITEM_WITHDRAWING = 1 << 6;
        public static final int FLAG_ITEM_WITHDRAWN = 1 << 7;
        public static final int FLAG_RECEIVED_INITIAL_ITEMS = 1 << 8;
        public static final int FLAG_STRUCTURES_RELEASED = 1 << 9;

        public BlockPos pen = null;
        public BlockPos stable = null;
        public BlockPos chestDeposit = null;
        public BlockPos savedJobPosition = null;
        public BlockPos personalChest = null;

        public Item feedingFood = null;
        public AnimalEntity activeTarget = null;

        public final List<AnimalEntity> fedAnimals = new ArrayList<>();
        public final Map<UUID, Long> lastFeedTimes = new HashMap<>();
        public List<AnimalEntity> feedingPair = new ArrayList<>();
        public final List<ButcherHelper> butcherTargets = new ArrayList<>();

        public int butcherLevel = 1;

        public boolean hasFlag(int mask) {
            return (flags & mask) != 0;
        }

        public void setFlag(int mask, boolean value) {
            flags = value ? (flags | mask) : (flags & ~mask);
        }
    }
}