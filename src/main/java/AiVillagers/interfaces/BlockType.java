package AiVillagers.interfaces;

import net.minecraft.block.DoorBlock;
import net.minecraft.block.FenceGateBlock;
import net.minecraft.sound.SoundEvent;
import net.minecraft.sound.SoundEvents;
import net.minecraft.state.property.BooleanProperty;

public enum BlockType {
    FENCE(FenceGateBlock.OPEN, SoundEvents.BLOCK_FENCE_GATE_OPEN, SoundEvents.BLOCK_FENCE_GATE_CLOSE),
    DOOR(DoorBlock.OPEN, SoundEvents.BLOCK_WOODEN_DOOR_OPEN, SoundEvents.BLOCK_WOODEN_DOOR_CLOSE);

    private final BooleanProperty openProp;
    private final SoundEvent openSound, closeSound;

    BlockType(BooleanProperty prop, SoundEvent openSound, SoundEvent closeSound) {
        this.openProp = prop;
        this.openSound = openSound;
        this.closeSound = closeSound;
    }

    public BooleanProperty openProp() {
        return openProp;
    }

    public SoundEvent openSound() {
        return openSound;
    }

    public SoundEvent closeSound() {
        return closeSound;
    }
}

