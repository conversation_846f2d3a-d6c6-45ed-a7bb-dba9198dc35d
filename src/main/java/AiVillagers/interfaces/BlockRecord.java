package AiVillagers.interfaces;

import net.minecraft.util.math.BlockPos;

public class BlockRecord {
    private BlockPos pos;
    private long lastTick = -20;

    public void record(BlockPos p, long tick) {
        pos = p;
        lastTick = tick;
    }

    public boolean canAct(long tick) {
        return tick - lastTick >= 10;
    }

    public BlockPos getPos() {
        return pos;
    }

    public void clear() {
        pos = null;
        lastTick = -20;
    }
}
