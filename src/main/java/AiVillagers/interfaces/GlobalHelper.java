package AiVillagers.interfaces;

import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.BlockPos;

import java.util.List;

import static java.util.Objects.requireNonNull;

public class GlobalHelper {

    public static class SleepProtectionState {
        public static final boolean SILENT_WAKE = false;
        public static final boolean NO_UPDATE_SLEEPING_POSITION = false;
    }
    public record BedProtectionContext(
            VillagerEntity villager,
            BlockPos bedPosition,
            List<? extends PlayerEntity> players
    ) {
        public BedProtectionContext {
            requireNonNull(villager, "Villager cannot be null");
            requireNonNull(bedPosition, "Bed position cannot be null");
            requireNonNull(players, "Players collection cannot be null");
        }
    }
}
