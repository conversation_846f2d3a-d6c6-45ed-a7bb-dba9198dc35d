package AiVillagers.interfaces;

import AiVillagers.entities.fishing.VillagerFishingBobberEntity;
import net.minecraft.util.math.BlockPos;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class FisherHelper {

    public static class FisherState {
        public boolean isCurrentlyFishing = false;
        public boolean isStoringItemsInBarrel = false;
        public boolean fishingResultPredetermined = false;
        public boolean willFishingSucceed = false;
        public boolean fishingEventTriggered = false;
        public boolean needsHandCleanup = false;
        public boolean hasItemsForBarrel = false;

        public int fishingLookTimer = 0;
        public int fishingCooldownTimer = 0;
        public int handCleanupTimer = 0;
        public int barrelInteractionTimer = 0;
        public int fishingEventTime = 0;
        public int animationDuration = 0;
        public int consecutiveFailures = 0;
        public int consecutiveSuccesses = 0;

        public long lastFishingAttemptTime = 0;
        public long lastWorkSessionTime = -1;
        public long lastSpotSearchTime = 0;
        public long personalBarrelWorkTime = -1;
        public long lastItemCheckDay = -1;

        public BlockPos currentFishingTarget = null;
        public BlockPos lastSuccessfulFishingSpot = null;
        public BlockPos assignedWorkStation = null;
        public BlockPos targetWaterPosition = null;

        public VillagerFishingBobberEntity villagerBobber = null;

        public final Map<BlockPos, List<BlockPos>> validSpotsCache = new ConcurrentHashMap<>();
        public final Map<BlockPos, Long> spotsCacheTimestamp = new ConcurrentHashMap<>();

        public static final int[] FISHING_COOLDOWN_BASE_OPTIONS = {520, 580, 640};

        public void clearAllCaches() {
            validSpotsCache.clear();
            spotsCacheTimestamp.clear();
        }
    }
}