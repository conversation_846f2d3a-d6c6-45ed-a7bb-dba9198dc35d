package AiVillagers.entities;

import AiVillagers.entities.fishing.VillagerFishingBobberEntity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.SpawnGroup;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.util.Identifier;

import static AiVillagers.core.AiVillagersMod.LOGGER;
import static AiVillagers.core.AiVillagersMod.MOD_ID;

public class ModEntities {

    public static final RegistryKey<EntityType<?>> VILLAGER_FISHING_BOBBER_KEY =
        RegistryKey.of(RegistryKeys.ENTITY_TYPE, Identifier.of(MOD_ID, "villager_fishing_bobber"));

    public static final EntityType<VillagerFishingBobberEntity> VILLAGER_FISHING_BOBBER =
        EntityType.Builder.<VillagerFishingBobberEntity>create(VillagerFishingBobberEntity::new, SpawnGroup.MISC)
            .dimensions(0.15F, 0.15F)
            .maxTrackingRange(64)
            .trackingTickInterval(1)
            .build(VILLAGER_FISHING_BOBBER_KEY);

    public static void registerEntities() {
        Registry.register(Registries.ENTITY_TYPE, VILLAGER_FISHING_BOBBER_KEY, VILLAGER_FISHING_BOBBER);
        LOGGER.info("Entidades personalizadas de AiVillagers registradas");
    }
}
