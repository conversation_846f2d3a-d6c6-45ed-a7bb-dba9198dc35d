package AiVillagers.entities.fishing;

import AiVillagers.entities.ModEntities;
import AiVillagers.interfaces.VillagerFishHookInterface;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.ExperienceOrbEntity;
import net.minecraft.entity.MovementType;
import net.minecraft.entity.data.DataTracker;
import net.minecraft.entity.data.TrackedData;
import net.minecraft.entity.data.TrackedDataHandlerRegistry;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.entity.projectile.ProjectileEntity;
import net.minecraft.entity.projectile.ProjectileUtil;
import net.minecraft.fluid.FluidState;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.registry.tag.FluidTags;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundEvents;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.Objects;

public final class VillagerFishingBobberEntity extends ProjectileEntity {
    private static final TrackedData<Integer> HOOK_ENTITY_ID = DataTracker.registerData(VillagerFishingBobberEntity.class, TrackedDataHandlerRegistry.INTEGER);
    private static final TrackedData<Boolean> CAUGHT_FISH = DataTracker.registerData(VillagerFishingBobberEntity.class, TrackedDataHandlerRegistry.BOOLEAN);
    private static final TrackedData<Float> MAXIMUM_SINKING_DEPTH = DataTracker.registerData(VillagerFishingBobberEntity.class, TrackedDataHandlerRegistry.FLOAT);
    private VillagerEntity villagerOwner;
    private State state = State.FLYING;
    private boolean isEmbeddedInGround;
    private Entity hookedEntity;
    private Vec3d originalTargetPosition;
    private boolean isInOpenWaterArea = true;
    private int ticksOutOfWater;
    private int fishingHookCountdown;
    private boolean isFishingResultPredetermined;
    private boolean willFishingSucceed;
    private int fishingEventTime;
    private boolean isFishingEventTriggered;
    private int fishApproachCountdown;
    private float fishAngle;
    private boolean hasFishApproachStarted;
    private int fishApproachAnimationTimer;
    private int fishApproachAnimationDuration;
    private int sinkingAnimationPhase;
    private boolean hasSplashSoundPlayed;
    private double targetDepth;

    // Constructor basico que inicializa la entidad bobber con tipo y mundo
    public VillagerFishingBobberEntity(final EntityType<? extends VillagerFishingBobberEntity> entityType, final World world) {
        super(Objects.requireNonNull(entityType, "EntityType cannot be null"), Objects.requireNonNull(world, "World cannot be null"));
        this.originalTargetPosition = Vec3d.ZERO;
        initializeEntityProperties();
    }

    // Constructor principal que crea un bobber para un aldeano especifico con posicion objetivo
    public VillagerFishingBobberEntity(final World world, final VillagerEntity villagerEntity, final Vec3d targetPos) {
        this(ModEntities.VILLAGER_FISHING_BOBBER, world);

        this.villagerOwner = Objects.requireNonNull(villagerEntity, "Villager owner cannot be null");
        this.originalTargetPosition = Objects.requireNonNull(targetPos, "Target position cannot be null");

        initializeVillagerFishing(villagerEntity, targetPos);
    }

    // Configura las propiedades basicas de la entidad bobber
    private void initializeEntityProperties() {
        this.setNoGravity(false);
        this.noClip = false;
    }

    // Inicializa el sistema de pesca del aldeano configurando trayectoria y sonidos
    private void initializeVillagerFishing(final VillagerEntity villagerEntity, final Vec3d targetPos) {
        ((VillagerFishHookInterface) villagerEntity).aiVillagersFabric$setFishHook(this);
        this.getDataTracker().set(HOOK_ENTITY_ID, villagerEntity.getId() + 1);

        final var startPos = calculateStartPosition(villagerEntity);
        final var trajectoryData = calculateTrajectory(startPos, targetPos);

        setupEntityOrientation(startPos, trajectoryData);
        this.setVelocity(calculateInitialVelocity(targetPos, startPos, trajectoryData.horizontalDistance()));

        if (!getWorld().isClient) {
            villagerEntity.playSound(SoundEvents.ENTITY_FISHING_BOBBER_THROW, 0.5F, 1.0F);
        }
    }

    // Calcula la posicion inicial desde donde el aldeano lanza el bobber
    private static Vec3d calculateStartPosition(final VillagerEntity villagerEntity) {
        return villagerEntity.getPos().add(0, villagerEntity.getStandingEyeHeight() * 0.8, 0);
    }

    private record TrajectoryData(Vec3d direction, double horizontalDistance, float yaw, float pitch) {}

    // Calcula los datos de trayectoria incluyendo direccion, distancia y angulos
    private TrajectoryData calculateTrajectory(final Vec3d startPos, final Vec3d targetPos) {
        final var direction = targetPos.subtract(startPos);
        final var horizontalDistance = Math.sqrt(direction.x * direction.x + direction.z * direction.z);
        final var yaw = (float)(MathHelper.atan2(-direction.x, direction.z) * 57.29578);
        final var pitch = (float)(MathHelper.atan2(direction.y, horizontalDistance) * 57.29578);

        return new TrajectoryData(direction, horizontalDistance, yaw, pitch);
    }

    // Configura la orientacion y posicion inicial de la entidad bobber
    private void setupEntityOrientation(final Vec3d startPos, final TrajectoryData trajectoryData) {
        this.refreshPositionAndAngles(startPos.x, startPos.y, startPos.z, trajectoryData.yaw(), trajectoryData.pitch());
        this.setYaw(trajectoryData.yaw());
        this.setPitch(trajectoryData.pitch());
        this.lastYaw = trajectoryData.yaw();
        this.lastPitch = trajectoryData.pitch();
    }

    // Inicializa el rastreador de datos para sincronizacion cliente-servidor
    @Override
    protected void initDataTracker(final DataTracker.Builder builder) {
        Objects.requireNonNull(builder, "DataTracker builder cannot be null");
        builder.add(HOOK_ENTITY_ID, 0);
        builder.add(CAUGHT_FISH, false);
        builder.add(MAXIMUM_SINKING_DEPTH, 2.0F);
    }

    // Calcula la velocidad inicial necesaria para alcanzar la posicion objetivo
    private static Vec3d calculateInitialVelocity(final Vec3d targetPos, final Vec3d startPos, final double horizontalDistance) {
        final var deltaY = targetPos.y - startPos.y;
        final var deltaX = targetPos.x - startPos.x;
        final var deltaZ = targetPos.z - startPos.z;

        final var flightTime = switch ((int) Math.clamp(horizontalDistance, 0, 10)) {
            case 0, 1, 2, 3 -> 0.8;
            case 4, 5, 6 -> 1.2;
            default -> 1.8;
        };

        final var velocityX = deltaX / flightTime;
        final var velocityZ = deltaZ / flightTime;
        final var velocityY = (deltaY / flightTime) + (0.5 * 0.08 * flightTime);

        return new Vec3d(velocityX, velocityY, velocityZ);
    }

    // Metodo principal que se ejecuta cada tick para actualizar el estado del bobber
    @Override
    public void tick() {
        this.baseTick();

        processGroundToWaterTransition();

        if (!ensureValidOwner()) {
            return;
        }

        if (isClientSide() || !shouldRemoveDueToInvalidOwner(this.villagerOwner)) {
            processEntityState();
        }
    }

    // Verifica que el aldeano propietario del bobber sea valido
    private boolean ensureValidOwner() {
        if (this.villagerOwner != null) {
            return true;
        }

        final var ownerId = this.getDataTracker().get(HOOK_ENTITY_ID);
        if (ownerId > 0) {
            final var entity = this.getWorld().getEntityById(ownerId - 1);
            this.villagerOwner = entity instanceof VillagerEntity villager ? villager : null;
        }

        if (this.villagerOwner == null && !isClientSide()) {
            this.discard();
            return false;
        }

        return this.villagerOwner != null;
    }

    // Verifica si estamos en el lado del cliente
    private boolean isClientSide() {
        return this.getWorld().isClient;
    }

    // Procesa el estado actual de la entidad bobber
    private void processEntityState() {
        if (this.isEmbeddedInGround) {
            processGroundedBobberState();
        } else {
            processActiveBobberState();
        }
    }

    // Maneja la transicion del bobber desde el suelo hacia el agua
    private void processGroundToWaterTransition() {
        if (!this.isEmbeddedInGround) {
            return;
        }

        final var currentPos = this.getBlockPos();
        final var world = this.getWorld();

        final var isTouchingWater = isPositionTouchingWater(world, currentPos);

        if (isTouchingWater) {
            this.isEmbeddedInGround = false;
            transitionToBobbing();
        }
    }

    // Verifica si una posicion esta tocando agua en cualquier direccion
    private static boolean isPositionTouchingWater(final World world, final BlockPos pos) {
        return world.getBlockState(pos).getFluidState().isIn(FluidTags.WATER) ||
               world.getBlockState(pos.up()).getFluidState().isIn(FluidTags.WATER) ||
               world.getBlockState(pos.down()).getFluidState().isIn(FluidTags.WATER);
    }

    // Cambia el estado del bobber a flotando en el agua
    private void transitionToBobbing() {
        if (this.state != State.BOBBING) {
            this.state = State.BOBBING;
            this.setVelocity(this.getVelocity().multiply(0.3, 0.2, 0.3));
        }
    }

    // Procesa el estado cuando el bobber esta incrustado en el suelo
    private void processGroundedBobberState() {
        final var currentPos = this.getBlockPos();
        if (this.getWorld().getBlockState(currentPos).isAir()) {
            this.isEmbeddedInGround = false;
        }
    }

    // Procesa el estado activo del bobber segun su estado actual
    private void processActiveBobberState() {
        final var blockPos = this.getBlockPos();
        final var fluidState = this.getWorld().getFluidState(blockPos);
        final var waterHeight = fluidState.isIn(FluidTags.WATER) ? fluidState.getHeight(this.getWorld(), blockPos) : 0.0F;
        final var inWater = waterHeight > 0.0F;

        switch (this.state) {
            case FLYING -> processFlyingBobberState(inWater);
            case HOOKED_IN_ENTITY -> processHookedEntityState();
            case BOBBING -> processBobbingInWaterState(blockPos, waterHeight, inWater);
        }

        updatePositionAndVelocity(fluidState, inWater);
    }

    // Procesa el estado cuando el bobber esta volando por el aire
    private void processFlyingBobberState(final boolean inWater) {
        if (this.hookedEntity != null) {
            transitionToHookedState();
            return;
        }

        if (inWater) {
            initializeBobbingInWater();
        } else {
            detectAndProcessCollisions();
        }
    }

    // Cambia el estado del bobber a enganchado en una entidad
    private void transitionToHookedState() {
        this.setVelocity(Vec3d.ZERO);
        this.state = State.HOOKED_IN_ENTITY;
    }

    // Procesa el estado cuando el bobber esta enganchado en una entidad
    private void processHookedEntityState() {
        if (this.hookedEntity == null) {
            return;
        }

        if (isHookedEntityValid()) {
            updatePositionToHookedEntity();
        } else {
            releaseHookedEntity();
        }
    }

    // Verifica si la entidad enganchada sigue siendo valida
    private boolean isHookedEntityValid() {
        return !this.hookedEntity.isRemoved() && this.hookedEntity.getWorld() == this.getWorld();
    }

    // Actualiza la posicion del bobber para seguir a la entidad enganchada
    private void updatePositionToHookedEntity() {
        this.setPosition(
            this.hookedEntity.getX(),
            this.hookedEntity.getBodyY(0.8),
            this.hookedEntity.getZ()
        );
    }

    // Libera la entidad enganchada y vuelve al estado de vuelo
    private void releaseHookedEntity() {
        this.hookedEntity = null;
        this.getDataTracker().set(HOOK_ENTITY_ID, 0);
        this.state = State.FLYING;
    }

    // Procesa el estado cuando el bobber esta flotando en el agua
    private void processBobbingInWaterState(final BlockPos blockPos, final float waterHeight, final boolean inWater) {
        if (this.isEmbeddedInGround && inWater) {
            this.isEmbeddedInGround = false;
        }

        final var velocity = this.getVelocity();
        final var currentWaterSurfaceY = blockPos.getY() + waterHeight;
        final var distanceFromSurface = this.getY() + velocity.y - currentWaterSurfaceY;

        evaluateOpenWaterConditions(blockPos);
        updateWaterTicksAndPhysics(inWater, blockPos, velocity, distanceFromSurface);
    }

    // Actualiza los ticks fuera del agua y procesa la fisica del bobber
    private void updateWaterTicksAndPhysics(final boolean inWater, final BlockPos blockPos, final Vec3d velocity, final double distanceFromSurface) {
        if (inWater) {
            this.ticksOutOfWater = Math.max(0, this.ticksOutOfWater - 1);
            processWaterBobbingPhysics(blockPos, velocity, distanceFromSurface);
        } else {
            this.ticksOutOfWater = Math.min(10, this.ticksOutOfWater + 1);
        }
    }

    // Inicializa el estado de flotacion del bobber en el agua
    private void initializeBobbingInWater() {
        this.setVelocity(this.getVelocity().multiply(0.3, 0.2, 0.3));
        this.state = State.BOBBING;

        positionBobberInWater();
        playSplashSoundIfNeeded();
    }

    // Posiciona el bobber en el agua en la ubicacion objetivo o la mas cercana
    private void positionBobberInWater() {
        final var targetBlockPos = BlockPos.ofFloored(this.originalTargetPosition);
        final var world = this.getWorld();

        if (world.getBlockState(targetBlockPos).getFluidState().isIn(FluidTags.WATER)) {
            positionAtWaterBlock(targetBlockPos);
        } else {
            final var nearestWater = locateNearestWaterBlock(this.getBlockPos());
            if (nearestWater != null) {
                positionAtWaterBlock(nearestWater);
            }
        }
    }

    // Posiciona el bobber en el centro de un bloque de agua especifico
    private void positionAtWaterBlock(final BlockPos waterBlock) {
        final var world = this.getWorld();
        final var centerX = waterBlock.getX() + 0.5;
        final var centerZ = waterBlock.getZ() + 0.5;
        final var waterSurfaceY = waterBlock.getY() + world.getFluidState(waterBlock).getHeight(world, waterBlock);

        this.setPosition(centerX, waterSurfaceY + 0.1, centerZ);
    }

    // Reproduce el sonido de salpicadura cuando el bobber toca el agua
    private void playSplashSoundIfNeeded() {
        if (!hasSplashSoundPlayed && !isClientSide()) {
            final var pitchVariation = (this.random.nextFloat() - this.random.nextFloat()) * 0.4F;
            this.playSound(SoundEvents.ENTITY_GENERIC_SPLASH, 0.25F, 1.0F + pitchVariation);
            hasSplashSoundPlayed = true;
        }
    }

    // Evalua si el bobber esta en condiciones de agua abierta para pescar
    private void evaluateOpenWaterConditions(final BlockPos blockPos) {
        if (this.fishingHookCountdown <= 0 && this.fishApproachCountdown <= 0) {
            this.isInOpenWaterArea = true;
        } else {
            this.isInOpenWaterArea = this.isInOpenWaterArea &&
                                   this.ticksOutOfWater < 10 &&
                                   this.isPositionInOpenWater(blockPos);
        }
    }

    // Procesa la fisica de flotacion del bobber en el agua
    private void processWaterBobbingPhysics(final BlockPos blockPos, final Vec3d velocity, double distanceFromSurface) {

        if (this.hasFishBeenCaught()) {
            this.updateSmoothSinkingAnimation(blockPos, velocity);
            return;
        }

        distanceFromSurface = adjustSurfaceDistance(distanceFromSurface);
        final var bobbingForce = calculateBobbingForce(distanceFromSurface);
        applyBobbingVelocity(velocity, bobbingForce);

        if (!isClientSide()) {
            this.updateFishingGameLogic();
            this.updateAllAnimations(blockPos, velocity);
        }
    }

    // Ajusta la distancia desde la superficie para evitar valores muy pequeños
    private static double adjustSurfaceDistance(double distanceFromSurface) {
        if (Math.abs(distanceFromSurface) < 0.01) {
            return distanceFromSurface + Math.signum(distanceFromSurface) * 0.1;
        }
        return distanceFromSurface;
    }

    // Calcula la fuerza de flotacion basada en la distancia desde la superficie
    private double calculateBobbingForce(final double distanceFromSurface) {
        var bobbingForce = -distanceFromSurface * 0.1;

        if (this.age % 60 == 0) {
            final var randomSink = this.random.nextDouble() * 0.03 - 0.01;
            bobbingForce += randomSink;
        }

        return bobbingForce;
    }

    // Aplica la velocidad de flotacion al bobber
    private void applyBobbingVelocity(final Vec3d velocity, final double bobbingForce) {
        final var newVelocity = new Vec3d(
            velocity.x * 0.95,
            velocity.y + bobbingForce,
            velocity.z * 0.95
        );
        this.setVelocity(newVelocity);
    }

    // Actualiza la posicion y velocidad del bobber aplicando fisica
    private void updatePositionAndVelocity(final FluidState fluidState, final boolean inWater) {
        applyGravityBasedOnState(fluidState, inWater);

        this.move(MovementType.SELF, this.getVelocity());
        this.updateRotation();

        handleGroundCollision();
        applyVelocityDamping();
        this.refreshPosition();
    }

    // Aplica gravedad diferente segun el estado del bobber
    private void applyGravityBasedOnState(final FluidState fluidState, final boolean inWater) {
        final var gravityForce = switch (this.state) {
            case FLYING -> fluidState.isIn(FluidTags.WATER) ? -0.03 : -0.08;
            case BOBBING -> inWater ? 0.0 : -0.08;
            case HOOKED_IN_ENTITY -> 0.0;
        };

        if (gravityForce != 0.0) {
            this.setVelocity(this.getVelocity().add(0.0, gravityForce, 0.0));
        }
    }

    // Maneja las colisiones del bobber con el suelo
    private void handleGroundCollision() {
        if (this.state == State.FLYING && (this.isOnGround() || this.horizontalCollision)) {
            this.setVelocity(Vec3d.ZERO);
        }
    }

    // Aplica amortiguacion a la velocidad del bobber
    private void applyVelocityDamping() {
        this.setVelocity(this.getVelocity().multiply(0.92));
    }

    // Verifica si el bobber debe eliminarse debido a un propietario invalido
    private boolean shouldRemoveDueToInvalidOwner(final VillagerEntity villager) {
        Objects.requireNonNull(villager, "Villager cannot be null for validation");

        if (villager.isRemoved() || !villager.isAlive()) {
            this.discard();
            return true;
        }

        if (this.squaredDistanceTo(villager) > 1024.0) {
            this.discard();
            return true;
        }

        return false;
    }

    // Detecta y procesa colisiones del bobber
    private void detectAndProcessCollisions() {
        final var hitResult = ProjectileUtil.getCollision(this, this::canHitEntity);
        this.onCollision(hitResult);
    }

    // Determina si el bobber puede golpear una entidad especifica
    private boolean canHitEntity(final Entity entity) {
        return super.canHit(entity) ||
               (entity.isAlive() && entity instanceof VillagerEntity && entity != this.villagerOwner);
    }

    // Maneja el impacto del bobber con una entidad
    @Override
    protected void onEntityHit(final EntityHitResult entityHitResult) {
        super.onEntityHit(entityHitResult);

        if (isClientSide()) {
            return;
        }

        final var hitEntity = entityHitResult.getEntity();
        this.hookedEntity = hitEntity;

        final var entityId = hitEntity != null ? hitEntity.getId() + 1 : 0;
        this.getDataTracker().set(HOOK_ENTITY_ID, entityId);
    }

    // Maneja el impacto del bobber con un bloque
    @Override
    protected void onBlockHit(final BlockHitResult blockHitResult) {
        super.onBlockHit(blockHitResult);

        final var normalizedVelocity = this.getVelocity().normalize();
        final var impactDistance = blockHitResult.squaredDistanceTo(this);

        this.setVelocity(normalizedVelocity.multiply(impactDistance));
        this.isEmbeddedInGround = true;
    }

    // Actualiza la lógica del juego de pesca
    private void updateFishingGameLogic() {
        if (isFishingResultPredetermined) {
            handlePredeterminedFishingLogic();
        } else {
            handleStandardFishingLogic();
        }
    }

    // Maneja la logica de pesca con resultado predeterminado
    private void handlePredeterminedFishingLogic() {
        if (isFishingEventTriggered && willFishingSucceed && !hasFishBeenCaught()) {
            this.getDataTracker().set(CAUGHT_FISH, true);
        }
    }

    // Maneja la logica de pesca estandar con countdown
    private void handleStandardFishingLogic() {
        if (this.fishingHookCountdown > 0) {
            --this.fishingHookCountdown;
            if (this.fishingHookCountdown <= 0) {
                this.getDataTracker().set(CAUGHT_FISH, false);
            }
        }
    }

    // Configura el resultado de la pesca antes de que ocurra
    public void configureFishingOutcome(final boolean willSucceed, final int eventTime) {
        this.isFishingResultPredetermined = true;
        this.willFishingSucceed = willSucceed;
        this.fishingEventTime = eventTime;
        this.isFishingEventTriggered = false;
        this.hasFishApproachStarted = false;
        this.fishApproachAnimationTimer = 0;
        this.hasSplashSoundPlayed = false;

        this.fishApproachAnimationDuration = 60 + this.random.nextInt(21);
    }

    // Inicia el evento de pesca con animaciones
    public void initiateFishingEvent() {
        if (!canInitiateFishingEvent()) {
            return;
        }

        if (!hasFishApproachStarted) {
            startFishApproachAnimation();
        }

        isFishingEventTriggered = true;
        beginFishingAnimation(willFishingSucceed);
    }

    // Verifica si se puede iniciar un evento de pesca
    private boolean canInitiateFishingEvent() {
        return isFishingResultPredetermined && !isFishingEventTriggered;
    }

    // Inicia la animacion de aproximacion del pez
    private void startFishApproachAnimation() {
        hasFishApproachStarted = true;
        fishApproachAnimationTimer = 0;
        this.fishAngle = this.random.nextFloat() * 360.0f;
        this.fishApproachCountdown = fishApproachAnimationDuration;
    }

    // Verifica si la posicion esta en agua abierta adecuada para pescar
    private boolean isPositionInOpenWater(BlockPos pos) {
        VillagerFishingBobberEntity.PositionType positionType = VillagerFishingBobberEntity.PositionType.INVALID;
        for(int i = -1; i <= 2; ++i) {
            VillagerFishingBobberEntity.PositionType positionType2 = this.determineBlockPositionType(pos.add(-2, i, -2), pos.add(2, i, 2));
            switch (positionType2) {
                case INVALID:
                    return false;
                case ABOVE_WATER:
                    if (positionType == VillagerFishingBobberEntity.PositionType.INVALID) {
                        return false;
                    }
                    break;
                case INSIDE_WATER:
                    if (positionType == VillagerFishingBobberEntity.PositionType.ABOVE_WATER) {
                        return false;
                    }
            }
            positionType = positionType2;
        }
        return true;
    }

    // Determina el tipo de posicion de un area de bloques
    private VillagerFishingBobberEntity.PositionType determineBlockPositionType(BlockPos start, BlockPos end) {
        return BlockPos.stream(start, end).map(pos -> {
            BlockState blockState = this.getWorld().getBlockState(pos);
            if (!blockState.isAir() && !blockState.isOf(Blocks.LILY_PAD)) {
                FluidState fluidState = blockState.getFluidState();
                return fluidState.isIn(FluidTags.WATER) && fluidState.isStill() && blockState.getCollisionShape(this.getWorld(), pos).isEmpty() ? VillagerFishingBobberEntity.PositionType.INSIDE_WATER : VillagerFishingBobberEntity.PositionType.INVALID;
            } else {
                return VillagerFishingBobberEntity.PositionType.ABOVE_WATER;
            }
        }).reduce((positionType, positionType2) -> positionType == positionType2 ? positionType : VillagerFishingBobberEntity.PositionType.INVALID).orElse(VillagerFishingBobberEntity.PositionType.INVALID);
    }

    // Localiza el bloque de agua mas cercano a la posicion actual
    private BlockPos locateNearestWaterBlock(BlockPos currentPos) {
        World world = this.getWorld();

        if (world.getBlockState(currentPos).getFluidState().isIn(FluidTags.WATER)) {
            return currentPos;
        }

        for (int radius = 1; radius <= 3; radius++) {
            for (BlockPos pos : BlockPos.iterateOutwards(currentPos, radius, 1, radius)) {
                if (world.getBlockState(pos).getFluidState().isIn(FluidTags.WATER)) {
                    if (world.getBlockState(pos.up()).isAir() ||
                        world.getBlockState(pos.up()).getFluidState().isEmpty()) {
                        return pos;
                    }
                }
            }
        }

        return null;
    }

    // Verifica si se ha capturado un pez
    private boolean hasFishBeenCaught() {
        return this.getDataTracker().get(CAUGHT_FISH);
    }

    // Obtiene el aldeano propietario del bobber
    public VillagerEntity getBobberOwner() {
        return this.villagerOwner;
    }

    @Override
    public boolean shouldRender(double distance) {
        return distance < (double)4096.0F;
    }

    // Inicia la animacion de pesca con particulas y efectos
    private void beginFishingAnimation(boolean isSuccessful) {
        this.state = State.BOBBING;
        this.fishAngle = this.random.nextFloat() * 360.0f;

        if (this.getWorld() instanceof ServerWorld serverWorld) {
            if (isSuccessful) {
                serverWorld.spawnParticles(ParticleTypes.FISHING,
                        this.getX(), this.getY() + 0.5, this.getZ(),
                        8, 0.2, 0.1, 0.2, 0.1);

                serverWorld.spawnParticles(ParticleTypes.SPLASH,
                    this.getX(), this.getY() + 0.3, this.getZ(),
                    5, 0.3, 0.1, 0.3, 0.1);

                serverWorld.spawnParticles(ParticleTypes.BUBBLE,
                    this.getX(), this.getY(), this.getZ(),
                    3, 0.1, 0.1, 0.1, 0.1);
            } else {
                serverWorld.spawnParticles(ParticleTypes.FISHING,
                    this.getX(), this.getY() + 0.3, this.getZ(),
                    4, 0.15, 0.05, 0.15, 0.05);

                serverWorld.spawnParticles(ParticleTypes.BUBBLE,
                        this.getX(), this.getY(), this.getZ(),
                        3, 0.1, 0.0, 0.1, 0.0);
            }
        }

        this.playSound(SoundEvents.ENTITY_FISHING_BOBBER_SPLASH, 0.5F, 1.0F);

        this.getDataTracker().set(CAUGHT_FISH, true);

        double[] possibleDepths = {1.0, 1.5, 2.0};
        this.targetDepth = possibleDepths[this.random.nextInt(possibleDepths.length)];
        this.sinkingAnimationPhase = 1;

        if (isSuccessful) {
            createExperienceReward();
        }
    }
    
    // Actualiza todas las animaciones del bobber incluyendo aproximacion de peces
    private void updateAllAnimations(BlockPos blockPos, Vec3d currentVelocity) {
        if (isFishingResultPredetermined && !isFishingEventTriggered) {
            int ticksUntilEvent = fishingEventTime - this.age;

            if (ticksUntilEvent <= fishApproachAnimationDuration && !hasFishApproachStarted) {
                hasFishApproachStarted = true;
                fishApproachAnimationTimer = 0;
                this.fishAngle = this.random.nextFloat() * 360.0f;
                this.fishApproachCountdown = fishApproachAnimationDuration;
            }

            if (hasFishApproachStarted && fishApproachAnimationTimer < fishApproachAnimationDuration) {
                if (this.getWorld() instanceof ServerWorld serverWorld && this.fishApproachCountdown > 0) {
                    int i = 1;
                    this.fishApproachCountdown -= i;

                    if (this.fishApproachCountdown > 0) {
                        this.fishAngle += (float)this.random.nextTriangular(0.0, 9.188);
                        float f = this.fishAngle * ((float)Math.PI / 180F);
                        float g = MathHelper.sin(f);
                        float h = MathHelper.cos(f);

                        double d = this.getX() + (double)(g * (float)this.fishApproachCountdown * 0.1F);
                        double e = (float)MathHelper.floor(this.getY()) + 1.0F;
                        double j = this.getZ() + (double)(h * (float)this.fishApproachCountdown * 0.1F);

                        BlockState blockState = serverWorld.getBlockState(BlockPos.ofFloored(d, e - 1.0, j));
                        if (blockState.isOf(Blocks.WATER)) {
                            if (this.random.nextFloat() < 0.15F) {
                                serverWorld.spawnParticles(ParticleTypes.BUBBLE, d, e - 0.1, j, 1, g, 0.1, h, 0.0);
                            }

                            float k = g * 0.04F;
                            float l = h * 0.04F;
                            serverWorld.spawnParticles(ParticleTypes.FISHING, d, e, j, 0, l, 0.01, -k, 1.0);
                            serverWorld.spawnParticles(ParticleTypes.FISHING, d, e, j, 0, -l, 0.01, k, 1.0);
                        }
                    }
                }
                fishApproachAnimationTimer++;
            }
        }
        
        if (isFishingResultPredetermined && isFishingEventTriggered && !willFishingSucceed && this.hasFishBeenCaught()) {
            int ticksSinceEvent = this.age - fishingEventTime;

            if (ticksSinceEvent >= 30 && ticksSinceEvent <= 50) {
                if (this.getWorld() instanceof ServerWorld serverWorld) {
                    if (ticksSinceEvent % 4 == 0) {
                        float escapeAngle = this.random.nextFloat() * 360.0f;
                        double escapeDistance = 1.0 + this.random.nextDouble() * 2.0;
                        double escapeX = this.getX() + Math.cos(Math.toRadians(escapeAngle)) * escapeDistance;
                        double escapeZ = this.getZ() + Math.sin(Math.toRadians(escapeAngle)) * escapeDistance;

                        serverWorld.spawnParticles(ParticleTypes.FISHING,
                            escapeX, this.getY() - 0.2, escapeZ,
                            1, 0.1, 0.05, 0.1, 0.1);

                        if (this.random.nextFloat() < 0.4f) {
                            serverWorld.spawnParticles(ParticleTypes.BUBBLE,
                                escapeX, this.getY() - 0.1, escapeZ,
                                1, 0.05, 0.05, 0.05, 0.05);
                        }
                    }
                }
            }

            if (ticksSinceEvent >= 50) {
                this.getDataTracker().set(CAUGHT_FISH, false);
            }
        }

        if (this.hasFishBeenCaught()) {
            this.updateSmoothSinkingAnimation(blockPos, currentVelocity);
        }
    }

    // Crea orbes de experiencia como recompensa por pescar exitosamente
    private void createExperienceReward() {
        if (this.getWorld() instanceof ServerWorld serverWorld && villagerOwner != null) {
            Vec3d villagerPos = villagerOwner.getPos();
            double offsetX = (this.random.nextDouble() - 0.5) * 2.0;
            double offsetZ = (this.random.nextDouble() - 0.5) * 2.0;
            double orbX = villagerPos.x + offsetX;
            double orbY = villagerPos.y + 0.5;
            double orbZ = villagerPos.z + offsetZ;

            int experienceValue = 1 + this.random.nextInt(2);
            ExperienceOrbEntity experienceOrb = new ExperienceOrbEntity(serverWorld, orbX, orbY, orbZ, experienceValue);

            experienceOrb.setVelocity(
                (this.random.nextDouble() - 0.5) * 0.2,
                0.1 + this.random.nextDouble() * 0.1,
                (this.random.nextDouble() - 0.5) * 0.2
            );

            serverWorld.spawnEntity(experienceOrb);
        }
    }

    // Actualiza la animacion suave de hundimiento y flotacion del bobber
    private void updateSmoothSinkingAnimation(BlockPos blockPos, Vec3d currentVelocity) {
        double waterSurfaceY = blockPos.getY() + this.getWorld().getFluidState(blockPos).getHeight(this.getWorld(), blockPos);
        double currentY = this.getY();

        double velocityPhase1 = - (0.25 * targetDepth) / 10;
        double velocityPhase2 = - (0.75 * targetDepth) / 20;
        double velocityPhase3 = 0.035;

        if (sinkingAnimationPhase == 1) {
            this.setVelocity(currentVelocity.x * 0.9, velocityPhase1, currentVelocity.z * 0.9);
            this.move(MovementType.SELF, this.getVelocity());
            this.velocityModified = true;
            if (this.age % 10 == 0) sinkingAnimationPhase = 2;

        } else if (sinkingAnimationPhase == 2) {
            this.setVelocity(currentVelocity.x * 0.9, velocityPhase2, currentVelocity.z * 0.9);
            this.move(MovementType.SELF, this.getVelocity());
            this.velocityModified = true;
            if (this.age % 20 == 0) sinkingAnimationPhase = 3;

        } else if (sinkingAnimationPhase == 3) {
            if (currentY >= waterSurfaceY) {
                this.getDataTracker().set(CAUGHT_FISH, false);
                return;
            }

            this.setVelocity(currentVelocity.x * 0.85, velocityPhase3, currentVelocity.z * 0.85);
            this.move(MovementType.SELF, this.getVelocity());
            this.velocityModified = true;
        }
    }

    public enum State {
        FLYING,
        HOOKED_IN_ENTITY,
        BOBBING
    }

    public enum PositionType {
        ABOVE_WATER,
        INSIDE_WATER,
        INVALID
    }
}