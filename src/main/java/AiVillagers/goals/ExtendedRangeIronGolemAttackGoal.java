package AiVillagers.goals;

import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.goal.MeleeAttackGoal;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.server.world.ServerWorld;

public class ExtendedRangeIronGolemAttackGoal extends MeleeAttackGoal {
    private static final double EXTENDED_ATTACK_RANGE = 3.0;
    private final IronGolemEntity ironGolem;
    private int attackCooldown;

    public ExtendedRangeIronGolemAttackGoal(IronGolemEntity mob, double speed, boolean pauseWhenMobIdle) {
        super(mob, speed, pauseWhenMobIdle);
        this.ironGolem = mob;
        this.attackCooldown = 0;
    }

    @Override
    public boolean canStart() {
        LivingEntity target = this.ironGolem.getTarget();
        if (target == null || !target.isAlive()) {
            return false;
        }
        
        double distanceToTarget = this.ironGolem.squaredDistanceTo(target);
        double maxAttackDistanceSquared = EXTENDED_ATTACK_RANGE * EXTENDED_ATTACK_RANGE;
        
        return distanceToTarget <= maxAttackDistanceSquared;
    }

    @Override
    public boolean shouldContinue() {
        LivingEntity target = this.ironGolem.getTarget();
        if (target == null || !target.isAlive()) {
            return false;
        }
        
        double distanceToTarget = this.ironGolem.squaredDistanceTo(target);
        double maxAttackDistanceSquared = EXTENDED_ATTACK_RANGE * EXTENDED_ATTACK_RANGE;
        
        return distanceToTarget <= maxAttackDistanceSquared * 1.5; // Un poco más de tolerancia para continuar
    }

    @Override
    public void tick() {
        LivingEntity target = this.ironGolem.getTarget();
        if (target == null) {
            return;
        }

        double distanceToTarget = this.ironGolem.squaredDistanceTo(target);
        double maxAttackDistanceSquared = EXTENDED_ATTACK_RANGE * EXTENDED_ATTACK_RANGE;

        this.ironGolem.getLookControl().lookAt(target);

        if (distanceToTarget <= maxAttackDistanceSquared) {
            this.ironGolem.getNavigation().stop();

            if (this.attackCooldown <= 0) {
                if (this.ironGolem.getWorld() instanceof ServerWorld serverWorld) {
                    this.ironGolem.tryAttack(serverWorld, target);
                    this.attackCooldown = 20;
                }
            }
        } else {
            this.ironGolem.getNavigation().startMovingTo(target, 1.0);
        }
        if (this.attackCooldown > 0) {
            this.attackCooldown--;
        }
    }
}
