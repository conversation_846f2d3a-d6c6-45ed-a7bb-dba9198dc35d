package AiVillagers.data;

import com.mojang.serialization.Codec;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.BlockPos;
import net.minecraft.datafixer.DataFixTypes;
import net.minecraft.world.PersistentState;
import net.minecraft.world.PersistentStateType;

import java.util.*;

public class FisherWorldData extends PersistentState {

    public final Map<BlockPos, Long> failedFishingSpots = new HashMap<>();
    public final Set<BlockPos> globallyReservedSpots = new HashSet<>();
    public final Map<BlockPos, Long> spotReservationTime = new HashMap<>();

    private static final Codec<Map.Entry<BlockPos, Long>> FAILED_SPOT_ENTRY_CODEC = RecordCodecBuilder.create(instance -> instance.group(
            BlockPos.CODEC.fieldOf("pos").forGetter(Map.Entry::getKey),
            Codec.LONG.fieldOf("time").forGetter(Map.Entry::getValue)
    ).apply(instance, Map::entry));

    public static final Codec<FisherWorldData> CODEC = RecordCodecBuilder.create(instance -> instance.group(
            Codec.list(FAILED_SPOT_ENTRY_CODEC).optionalFieldOf("failedSpots", new ArrayList<>()).forGetter(fwd ->
                    new ArrayList<>(fwd.failedFishingSpots.entrySet()))
    ).apply(instance, (failedEntries) -> {
        FisherWorldData data = new FisherWorldData();
        for (Map.Entry<BlockPos, Long> entry : failedEntries) {
            data.failedFishingSpots.put(entry.getKey(), entry.getValue());
        }
        return data;
    }));

    public static final PersistentStateType<FisherWorldData> TYPE =
            new PersistentStateType<>("fisher_world_data", FisherWorldData::new, _ -> CODEC, DataFixTypes.SAVED_DATA_STRUCTURE_FEATURE_INDICES);

    public FisherWorldData() {
    }

    public FisherWorldData(PersistentState.Context context) {
        this();
    }

    public static FisherWorldData get(ServerWorld world) {
        FisherWorldData data = world.getPersistentStateManager().getOrCreate(TYPE);

        data.globallyReservedSpots.clear();
        data.spotReservationTime.clear();

        return data;
    }

    public void addFailedSpot(BlockPos pos, long time) {
        failedFishingSpots.put(pos, time);
        markDirty();
    }

    public void removeFailedSpot(BlockPos pos) {
        failedFishingSpots.remove(pos);
        markDirty();
    }

    public boolean isSpotFailed(BlockPos pos, long currentTime, long maxAge) {
        Long failTime = failedFishingSpots.get(pos);
        if (failTime == null) return false;

        if (currentTime - failTime > maxAge) {
            removeFailedSpot(pos);
            return false;
        }

        return true;
    }

    public void reserveSpot(BlockPos pos, long gameTime) {
        globallyReservedSpots.add(pos);
        spotReservationTime.put(pos, gameTime);
        markDirty();
    }

    public void releaseSpot(BlockPos pos) {
        globallyReservedSpots.remove(pos);
        spotReservationTime.remove(pos);
        markDirty();
    }

    public boolean isSpotReserved(BlockPos pos) {
        return globallyReservedSpots.contains(pos);
    }

    public void cleanupOldData(long currentTime, long maxAge) {
        boolean changed = false;

        Iterator<Map.Entry<BlockPos, Long>> iterator = failedFishingSpots.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<BlockPos, Long> entry = iterator.next();
            if (currentTime - entry.getValue() > maxAge) {
                iterator.remove();
                changed = true;
            }
        }

        if (changed) {
            markDirty();
        }
    }

    public void limitCacheSize(int maxFailedSpots) {
        if (failedFishingSpots.size() > maxFailedSpots) {
            List<Map.Entry<BlockPos, Long>> entries = new ArrayList<>(failedFishingSpots.entrySet());
            entries.sort(Map.Entry.comparingByValue());

            int toRemove = failedFishingSpots.size() - maxFailedSpots;
            for (int i = 0; i < toRemove; i++) {
                failedFishingSpots.remove(entries.get(i).getKey());
            }

            markDirty();
        }
    }



    public void cleanupExpiredReservedSpots(long currentGameTime) {
        Set<BlockPos> spotsToRelease = new HashSet<>();
        long maxAgeInTicks = 400;

        for (Map.Entry<BlockPos, Long> entry : spotReservationTime.entrySet()) {
            BlockPos pos = entry.getKey();
            Long reservationTime = entry.getValue();

            if (reservationTime != null && (currentGameTime - reservationTime) > maxAgeInTicks) {
                spotsToRelease.add(pos);
            }
        }

        for (BlockPos pos : spotsToRelease) {
            releaseSpot(pos);
        }
    }
}
