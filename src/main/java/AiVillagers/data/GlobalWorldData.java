package AiVillagers.data;

import com.mojang.serialization.Codec;
import com.mojang.serialization.DataResult;
import com.mojang.serialization.codecs.RecordCodecBuilder;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.BlockPos;
import net.minecraft.datafixer.DataFixTypes;
import net.minecraft.world.PersistentState;
import net.minecraft.world.PersistentStateType;

import java.util.*;

public class GlobalWorldData extends PersistentState {
    public final Map<BlockPos, UUID> claimedStructures = new HashMap<>();
    public final Set<BlockPos> reservedChests = new HashSet<>();

    public static final Codec<UUID> UUID_CODEC = Codec.STRING.flatXmap(
            str -> {
                try {
                    return DataResult.success(UUID.fromString(str));
                } catch (IllegalArgumentException e) {
                    return DataResult.error(() -> "Invalid UUID: " + str);
                }
            },
            uuid -> DataResult.success(uuid.toString())
    );

    public static final Codec<GlobalWorldData> CODEC = RecordCodecBuilder.create(instance -> instance.group(
            Codec.unboundedMap(BlockPos.CODEC, UUID_CODEC).fieldOf("claimedStructures").forGetter(bwd -> bwd.claimedStructures),
            Codec.list(BlockPos.CODEC).fieldOf("reservedChests").forGetter(bwd -> new ArrayList<>(bwd.reservedChests))
    ).apply(instance, (claimed, reserved) -> {
        GlobalWorldData data = new GlobalWorldData();
        data.claimedStructures.putAll(claimed);
        data.reservedChests.addAll(reserved);
        return data;
    }));

    public static final PersistentStateType<GlobalWorldData> TYPE =
            new PersistentStateType<>("butcher_world_data", GlobalWorldData::new, _ -> CODEC, DataFixTypes.SAVED_DATA_STRUCTURE_FEATURE_INDICES);

    public GlobalWorldData() {
    }

    public GlobalWorldData(PersistentState.Context context) {
        this();
    }

    public static GlobalWorldData get(ServerWorld world) {
        return world.getPersistentStateManager().getOrCreate(TYPE);
    }
}