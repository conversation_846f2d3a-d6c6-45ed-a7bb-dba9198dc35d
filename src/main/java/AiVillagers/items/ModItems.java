package AiVillagers.items;

import net.minecraft.item.Item;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.util.Identifier;

import java.util.function.Function;

import static AiVillagers.core.AiVillagersMod.LOGGER;
import static AiVillagers.core.AiVillagersMod.MOD_ID;

public class ModItems {
    public static final Item VILLAGER_FISHING_ROD_CAST = registerItem(
            settings -> new Item(settings.maxCount(1)));

    private static Item registerItem(Function<Item.Settings, Item> itemFactory) {
        RegistryKey<Item> itemKey = RegistryKey.of(RegistryKeys.ITEM, Identifier.of(MOD_ID, "villager_fishing_rod_cast"));
        Item.Settings settings = new Item.Settings().registryKey(itemKey);
        Item item = itemFactory.apply(settings);
        return Registry.register(Registries.ITEM, itemKey, item);
    }

    public static void registerItems() {
        LOGGER.info("Items personalizados de AiVillagers registrados");
    }
}