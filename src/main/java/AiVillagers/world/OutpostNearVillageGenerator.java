package AiVillagers.world;

import net.fabricmc.fabric.api.event.lifecycle.v1.ServerChunkEvents;
import net.fabricmc.fabric.api.event.lifecycle.v1.ServerTickEvents;
import net.minecraft.entity.EquipmentSlot;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.mob.PillagerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.RegistryKey;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.structure.PoolStructurePiece;
import net.minecraft.structure.StructurePiece;
import net.minecraft.structure.StructurePiecesCollector;
import net.minecraft.structure.StructureTemplateManager;
import net.minecraft.structure.pool.StructurePool;
import net.minecraft.structure.pool.StructurePoolBasedGenerator;
import net.minecraft.structure.pool.alias.StructurePoolAliasLookup;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.BlockBox;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.util.math.random.Random;
import net.minecraft.village.raid.Raid;
import net.minecraft.world.Heightmap;
import net.minecraft.world.chunk.ChunkStatus;
import net.minecraft.world.chunk.WorldChunk;
import net.minecraft.world.gen.chunk.ChunkGenerator;
import net.minecraft.world.gen.structure.JigsawStructure;
import net.minecraft.world.gen.structure.Structure;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentHashMap;

public class OutpostNearVillageGenerator {

    private static final List<RegistryKey<Structure>> VILLAGE_KEYS = List.of(
            RegistryKey.of(RegistryKeys.STRUCTURE, Identifier.of("minecraft", "village_plains")),
            RegistryKey.of(RegistryKeys.STRUCTURE, Identifier.of("minecraft", "village_desert")),
            RegistryKey.of(RegistryKeys.STRUCTURE, Identifier.of("minecraft", "village_savanna")),
            RegistryKey.of(RegistryKeys.STRUCTURE, Identifier.of("minecraft", "village_snowy")),
            RegistryKey.of(RegistryKeys.STRUCTURE, Identifier.of("minecraft", "village_taiga"))
    );

    private static final Identifier OUTPOST_POOL = Identifier.of("minecraft", "pillager_outpost/base_plates");

    private static final Set<ChunkPos> villageChunksProcessed = new HashSet<>();
    private static final Queue<BlockPos> villagesRequiringOutpost = new ConcurrentLinkedQueue<>();
    private static final Queue<BlockPos> pendingOutposts = new ConcurrentLinkedQueue<>();
    private static final Map<ChunkPos, Boolean> outpostChunks = new HashMap<>();
    private static final Map<BlockPos, Long> scheduledRespawns = new ConcurrentHashMap<>();

    public static void register() {
        ServerChunkEvents.CHUNK_LOAD.register(OutpostNearVillageGenerator::onChunkLoad);
        ServerTickEvents.END_SERVER_TICK.register(OutpostNearVillageGenerator::onServerTick);
    }

    private static void onChunkLoad(ServerWorld world, WorldChunk chunk) {
        if (!chunk.getStatus().isAtLeast(ChunkStatus.STRUCTURE_STARTS)) return;

        ChunkPos pos = chunk.getPos();
        if (!villageChunksProcessed.add(pos)) return;

        boolean villageFound = VILLAGE_KEYS.stream().anyMatch(key -> {
            var structure = world.getRegistryManager().getOrThrow(RegistryKeys.STRUCTURE).getValueOrThrow(key);
            return chunk.getStructureStarts().get(structure) != null && chunk.getStructureStarts().get(structure).hasChildren();
        });

        if (villageFound) {
            if (world.random.nextFloat() <= 0.45f) {
                BlockPos villageCenter = new BlockPos(pos.getCenterX(), 0, pos.getCenterZ());
                villagesRequiringOutpost.add(villageCenter);
            }
        }
    }

    private static boolean isObstructed(ServerWorld world, BlockPos pos) {
        Box testArea = new Box(pos).expand(10, 20, 10);
        for (int x = (int) testArea.minX; x <= testArea.maxX; x++) {
            for (int z = (int) testArea.minZ; z <= testArea.maxZ; z++) {
                int topY = world.getTopY(Heightmap.Type.WORLD_SURFACE, x, z);
                if (topY < pos.getY() - 5 || topY > pos.getY() + 20) {
                    return true;
                }
            }
        }
        return false;
    }

    private static void onServerTick(MinecraftServer server) {
        ServerWorld world = server.getOverworld();

        if (!villagesRequiringOutpost.isEmpty()) {
            BlockPos villageCenter = villagesRequiringOutpost.poll();

            if (villageCenter != null) {
                for (int attempt = 0; attempt < 4; attempt++) {
                    int dx = (attempt + 1) * 156;
                    int x = villageCenter.getX() + dx;
                    int z = villageCenter.getZ();
                    ChunkPos candidateChunk = new ChunkPos(x >> 4, z >> 4);

                    if (world.isChunkLoaded(candidateChunk.x, candidateChunk.z)) {
                        int y = world.getTopY(Heightmap.Type.WORLD_SURFACE, x, z);
                        BlockPos candidatePos = new BlockPos(x, y, z);

                        if (!isObstructed(world, candidatePos)) {
                            pendingOutposts.add(candidatePos);
                        }
                        break;
                    }
                }
            }
        }

        if (!pendingOutposts.isEmpty()) {
            BlockPos pos = pendingOutposts.peek();
            if (pos != null && world.isChunkLoaded(pos.getX() >> 4, pos.getZ() >> 4)) {
                pendingOutposts.poll();
                boolean success = generateOutpost(world, pos);

                if (success) {
                    scheduledRespawns.put(pos, world.getTime() + 2400);
                }
            }
        }

        long time = world.getTime();
        scheduledRespawns.forEach((pos, nextSpawnTime) -> {
            if (time >= nextSpawnTime) {
                spawnExtraPillagers(world, pos);
                scheduledRespawns.put(pos, time + 2400);
            }
        });
    }

    private static boolean generateOutpost(ServerWorld world, BlockPos pos) {
        RegistryEntry<StructurePool> poolEntry = world.getRegistryManager()
                .getOrThrow(RegistryKeys.TEMPLATE_POOL)
                .getEntry(OUTPOST_POOL)
                .orElse(null);

        if (poolEntry == null) {
            return false;
        }

        ChunkGenerator chunkGenerator = world.getChunkManager().getChunkGenerator();
        StructureTemplateManager templateManager = world.getStructureTemplateManager();
        Random random = world.getRandom();

        for (int dx = -3; dx <= 3; dx++) {
            for (int dz = -3; dz <= 3; dz++) {
                world.getChunk((pos.getX() >> 4) + dx, (pos.getZ() >> 4) + dz);
            }
        }

        Structure.Context context = new Structure.Context(
                world.getRegistryManager(),
                chunkGenerator,
                chunkGenerator.getBiomeSource(),
                world.getChunkManager().getNoiseConfig(),
                templateManager,
                world.getSeed(),
                new ChunkPos(pos),
                world,
                _ -> true
        );

        Optional<Structure.StructurePosition> optional = StructurePoolBasedGenerator.generate(
                context,
                poolEntry,
                Optional.empty(),
                5,
                pos,
                false,
                Optional.empty(),
                128,
                StructurePoolAliasLookup.EMPTY,
                JigsawStructure.DEFAULT_DIMENSION_PADDING,
                JigsawStructure.DEFAULT_LIQUID_SETTINGS
        );

        if (optional.isEmpty()) {
            return false;
        }

        StructurePiecesCollector collector = optional.get().generate();
        BlockBox generationBox = new BlockBox(
                pos.getX() - 50, pos.getY() - 20, pos.getZ() - 50,
                pos.getX() + 50, pos.getY() + 70, pos.getZ() + 50
        );

        for (StructurePiece piece : collector.toList().pieces()) {
            if (piece instanceof PoolStructurePiece poolPiece) {
                poolPiece.generate(world, world.getStructureAccessor(), chunkGenerator, random, generationBox, pos, false);
            }
        }

        BlockBox totalBoundingBox = collector.getBoundingBox();

        for (int chunkX = totalBoundingBox.getMinX() >> 4; chunkX <= totalBoundingBox.getMaxX() >> 4; chunkX++) {
            for (int chunkZ = totalBoundingBox.getMinZ() >> 4; chunkZ <= totalBoundingBox.getMaxZ() >> 4; chunkZ++) {
                ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);
                outpostChunks.put(chunkPos, true);
            }
        }

        Box spawnArea = new Box(pos.getX() - 10, pos.getY() - 5, pos.getZ() - 10, pos.getX() + 10, pos.getY() + 20, pos.getZ() + 10);
        spawnInitialPillagers(world, spawnArea);

        return true;
    }

    private static void spawnInitialPillagers(ServerWorld world, Box area) {
        Random random = world.random;
        double centerX = area.getCenter().x;
        double centerZ = area.getCenter().z;
        int baseY = (int) area.minY;
        int captainY = baseY + 3 + random.nextInt(3);

        for (int i = 0; i < 3; i++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double radius = random.nextDouble() * 2;
            double x = centerX + Math.cos(angle) * radius;
            double z = centerZ + Math.sin(angle) * radius;
            double y = world.getTopY(Heightmap.Type.WORLD_SURFACE, (int) x, (int) z) + 1;

            PillagerEntity pillager = new PillagerEntity(EntityType.PILLAGER, world);
            pillager.setPersistent();
            pillager.refreshPositionAndAngles(x, y, z, random.nextFloat() * 360.0F, 0);
            pillager.equipStack(EquipmentSlot.MAINHAND, new ItemStack(Items.CROSSBOW));
            world.spawnEntity(pillager);
        }

        for (int i = 0; i < 2; i++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double radius = 10 + random.nextDouble() * 3;
            double x = centerX + Math.cos(angle) * radius;
            double z = centerZ + Math.sin(angle) * radius;
            double y = world.getTopY(Heightmap.Type.WORLD_SURFACE, (int) x, (int) z) + 2;

            PillagerEntity pillager = new PillagerEntity(EntityType.PILLAGER, world);
            pillager.setPersistent();
            pillager.refreshPositionAndAngles(x, y, z, random.nextFloat() * 360.0F, 0);
            pillager.equipStack(EquipmentSlot.MAINHAND, new ItemStack(Items.CROSSBOW));
            world.spawnEntity(pillager);
        }

        PillagerEntity captain = new PillagerEntity(EntityType.PILLAGER, world);
        captain.setPersistent();
        captain.setPatrolLeader(true);
        captain.refreshPositionAndAngles(centerX, captainY, centerZ, random.nextFloat() * 360.0F, 0);
        captain.equipStack(EquipmentSlot.MAINHAND, new ItemStack(Items.CROSSBOW));
        ItemStack banner = Raid.createOminousBanner(world.getRegistryManager().getOrThrow(RegistryKeys.BANNER_PATTERN));
        captain.equipStack(EquipmentSlot.HEAD, banner);
        world.spawnEntity(captain);
    }

    private static void spawnExtraPillagers(ServerWorld world, BlockPos center) {
        Random random = world.random;
        int centerX = center.getX();
        int centerZ = center.getZ();

        for (int i = 0; i < 4; i++) {
            double angle = random.nextDouble() * 2 * Math.PI;
            double radius = 8 + random.nextDouble() * 8;
            double x = centerX + Math.cos(angle) * radius;
            double z = centerZ + Math.sin(angle) * radius;
            int y = world.getTopY(Heightmap.Type.WORLD_SURFACE, (int) x, (int) z) + 2;

            PillagerEntity pillager = new PillagerEntity(EntityType.PILLAGER, world);
            pillager.setPersistent();
            pillager.refreshPositionAndAngles(x + 0.5, y, z + 0.5, random.nextFloat() * 360.0F, 0);
            pillager.equipStack(EquipmentSlot.MAINHAND, new ItemStack(Items.CROSSBOW));
            world.spawnEntity(pillager);
        }
    }

    public static boolean isOutpostChunk(ChunkPos chunkPos) {
        return outpostChunks.containsKey(chunkPos);
    }
}