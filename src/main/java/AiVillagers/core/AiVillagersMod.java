package AiVillagers.core;

import AiVillagers.entities.ModEntities;
import AiVillagers.items.ModItems;
import AiVillagers.world.OutpostNearVillageGenerator;
import net.fabricmc.api.ModInitializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AiVillagersMod implements ModInitializer {
	public static final String MOD_ID = "ai_villagers";
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	@Override
	public void onInitialize() {
		LOGGER.info("Inicializando {}!", MOD_ID);

		ModItems.registerItems();

		ModEntities.registerEntities();

		OutpostNearVillageGenerator.register();
	}
}