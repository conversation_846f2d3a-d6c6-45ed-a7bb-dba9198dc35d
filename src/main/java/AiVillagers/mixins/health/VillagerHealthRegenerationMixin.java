package AiVillagers.mixins.health;

import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.passive.VillagerEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(VillagerEntity.class)
public abstract class VillagerHealthRegenerationMixin {

    @Unique
    private int healCooldown = 100;

    @Inject(method = "tick", at = @At("HEAD"))
    private void onTick(CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;

        if (!villager.getWorld().isClient() && villager.isAlive()) {
            double maxHealth = villager.getAttributeValue(EntityAttributes.MAX_HEALTH);
            float currentHealth = villager.getHealth();

            if (currentHealth < maxHealth) {
                healCooldown--;

                if (healCooldown <= 0) {
                    villager.heal(1.0F);
                    healCooldown = 100;
                }
            } else {
                healCooldown = 100;
            }
        }
    }
}