package AiVillagers.mixins.professions.butcher;

import AiVillagers.data.GlobalWorldData;
import AiVillagers.interfaces.ButcherHelper;
import com.mojang.datafixers.util.Pair;
import com.mojang.serialization.Codec;
import net.minecraft.entity.mob.MobEntity;
import net.minecraft.state.property.Properties;
import net.minecraft.block.BlockState;
import net.minecraft.block.entity.SmokerBlockEntity;
import net.minecraft.entity.ExperienceOrbEntity;
import net.minecraft.registry.Registries;
import AiVillagers.interfaces.AnimalEntityInterface;
import AiVillagers.interfaces.InventoryOp;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.brain.MemoryModuleType;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.passive.*;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.registry.RegistryKeys;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import net.minecraft.storage.ReadView;
import net.minecraft.storage.WriteView;
import net.minecraft.structure.PoolStructurePiece;
import net.minecraft.structure.StructurePiece;
import net.minecraft.structure.StructureStart;
import net.minecraft.util.Hand;
import net.minecraft.util.Identifier;
import net.minecraft.util.collection.DefaultedList;
import net.minecraft.util.math.*;
import net.minecraft.village.VillagerData;
import net.minecraft.village.VillagerProfession;
import net.minecraft.world.World;
import net.minecraft.block.Blocks;
import net.minecraft.block.entity.ChestBlockEntity;
import net.minecraft.block.entity.BlockEntity;
import net.minecraft.inventory.Inventory;
import net.minecraft.world.gen.StructureAccessor;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static AiVillagers.core.AiVillagersMod.LOGGER;

@Mixin(VillagerEntity.class)
public abstract class ButcherBehaviorMixin extends MobEntity implements ButcherHelper.ButcherAccess {

    @Unique private ButcherHelper.ButcherState state = new ButcherHelper.ButcherState();

    protected ButcherBehaviorMixin(EntityType<? extends MobEntity> entityType, World world) {
        super(entityType, world);
    }

    // Inicialización y Ciclo de Vida

    @Inject(method = "setVillagerData", at = @At("HEAD"))
    private void cachePreviousData(VillagerData data, CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity)(Object)this;
        this.state.previousData = villager.getVillagerData();
    }

    @Inject(method = "setVillagerData", at = @At("TAIL"))
    private void onProfessionChanged(VillagerData data, CallbackInfo ci) {
        RegistryEntry<VillagerProfession> previous = state.previousData.profession();
        RegistryEntry<VillagerProfession> current = data.profession();

        boolean wasButcher = previous.matchesKey(VillagerProfession.BUTCHER);
        boolean isNowButcher = current.matchesKey(VillagerProfession.BUTCHER);

        if (!wasButcher && isNowButcher) {
            this.state.fedAnimals.clear();
            this.state.lastFeedTimes.clear();
            this.state.butcherLevel = 1;
            LOGGER.info("Aldeano convertido en carnicero: {}", this);
        }
    }

    @Inject(method = "onDeath", at = @At("HEAD"))
    private void onVillagerDeath(DamageSource source, CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity)(Object)this;
        World world = villager.getWorld();

        if (world.isClient) {
            return;
        }

        RegistryEntry<VillagerProfession> profession = villager.getVillagerData().profession();
        boolean isButcher = profession.matchesKey(VillagerProfession.BUTCHER);
        if (!isButcher) {
            return;
        }

        UUID id = villager.getUuid();
        GlobalWorldData worldData = GlobalWorldData.get((ServerWorld) world);
        worldData.claimedStructures.entrySet().removeIf(entry -> entry.getValue().equals(id));
        if (state.chestDeposit != null) {
            worldData.reservedChests.remove(state.chestDeposit);
        }
        this.state.pen = null;
        this.state.stable = null;
        this.state.chestDeposit = null;
        LOGGER.info("Aldeano carnicero {} ha muerto, liberando estructuras y cofre.", id);
    }

    @Inject(method = "tick", at = @At("HEAD"))
    private void onVillagerTick(CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity)(Object)this;
        World world = villager.getWorld();

        if (world.isClient) return;

        UUID villagerId = villager.getUuid();
        GlobalWorldData worldData = GlobalWorldData.get((ServerWorld) world);
        RegistryEntry<VillagerProfession> profession = villager.getVillagerData().profession();
        boolean isButcher = profession.matchesKey(VillagerProfession.BUTCHER);

        if (isButcher && !state.hasFlag(ButcherHelper.ButcherState.FLAG_WAS_BUTCHER_ITEM)) {
            if (!state.hasFlag(ButcherHelper.ButcherState.FLAG_RECEIVED_INITIAL_ITEMS)) {
                manageInventory(villager, "add", List.of(new ItemStack(Items.WHEAT, 4), new ItemStack(Items.CARROT, 4), new ItemStack(Items.COAL, 2)));
                state.setFlag(ButcherHelper.ButcherState.FLAG_RECEIVED_INITIAL_ITEMS, true);
                state.setFlag(ButcherHelper.ButcherState.FLAG_HAS_BONUS, true);
            }
            state.butcherTickCounter = 0;
            state.setFlag(ButcherHelper.ButcherState.FLAG_WAS_BUTCHER_ITEM, true);
            state.setFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_RELEASED, false);
        } else if (isButcher) {
            state.butcherTickCounter++;
            if (state.butcherTickCounter >= 6000) {
                manageInventory(villager, "add", List.of(new ItemStack(Items.WHEAT, 2), new ItemStack(Items.CARROT, 2), new ItemStack(Items.COAL, 1)));
                state.butcherTickCounter = 0;
            }
            if (state.professionLossTimer > 0) {
                state.professionLossTimer = 0;
            }
        }

        if (state.hasFlag(ButcherHelper.ButcherState.FLAG_WAS_BUTCHER_ITEM)) {
            if (!isButcher) {
                manageInventory(villager, "clear", null);
                clearAxesFromInventory(villager);
                if (state.chestDeposit != null) worldData.reservedChests.remove(state.chestDeposit);
                if (state.personalChest != null) worldData.reservedChests.remove(state.personalChest);
                state.chestDeposit = null;
                state.personalChest = null;
                state.setFlag(ButcherHelper.ButcherState.FLAG_CHEST_OPEN, false);
                state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSIT_COMPLETED, false);
                state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSITING, false);
                state.chestAnimationTimer = 0;
                worldData.markDirty();
            }
        } else if (!isButcher && state.hasFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_ASSIGNED)) {
            worldData.claimedStructures.entrySet().removeIf(entry -> entry.getValue().equals(villagerId));
            state.pen = null;
            state.stable = null;
            state.setFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_ASSIGNED, false);
            state.setFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_RELEASED, true);
            worldData.markDirty();
        }

        if (!isButcher) {
            state.professionLossTimer++;
            if (state.professionLossTimer >= 2400 && !state.hasFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_RELEASED)) {
                worldData.claimedStructures.entrySet().removeIf(entry -> entry.getValue().equals(villagerId));
                state.pen = null;
                state.stable = null;
                state.setFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_ASSIGNED, false);
                state.setFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_RELEASED, true);
                worldData.markDirty();
            }
            state.setFlag(ButcherHelper.ButcherState.FLAG_WAS_BUTCHER_ITEM, false);
            state.butcherTickCounter = 0;
            state.setFlag(ButcherHelper.ButcherState.FLAG_HAS_BONUS, false);
            resetStatePreservingCooldowns();
            return;
        }

        if (state.freeze > 0) {
            villager.getNavigation().stop();
            state.freeze--;
            return;
        }

        if (!profession.matchesKey(VillagerProfession.BUTCHER)) {
            clearAxesFromInventory(villager);
            resetStatePreservingCooldowns();
            return;
        }

        int newLevel = villager.getVillagerData().level();
        if (newLevel != state.butcherLevel) {
            state.butcherLevel = newLevel;
        }
        ensureAxeForLevel(villager);

        if (!world.isClient && world instanceof ServerWorld server) {
            if (!state.hasFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_ASSIGNED)) {
                assignStructures(villager, server);
                state.setFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_ASSIGNED, true);
            }
            handleDailyRoutine(villager, server);
        }

        long time = world.getTimeOfDay() % 24000;
        boolean CleanupMainHand = (time >= 9000 && time < 9100);
        boolean CleanupStates = (time >= 9000 && time < 24000);

        if (CleanupMainHand) {
            villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
        }

        if (CleanupStates || villager.isSleeping()) {
            resetStatePreservingCooldowns();
            return;
        }


        if (isButcher && time >= 0 && time < 500 && state.personalChest != null && !state.hasFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWING)) {
            if (state.isFirstItemCycle()) {
                state.setFirstItemCycle(false);
            } else {
                state.setFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWING, true);
                villager.getNavigation().stop();
                catchItemsFromChest(villager, world, state.personalChest);
                return;
            }
        } else if (time >= 500 && !state.isFirstItemCycle()) {
            state.setFirstItemCycle(true);
        }

        if (!world.isClient) {
            if (state.chestDepositDelay > 0) state.chestDepositDelay--;
            if (state.feedingDelay > 0) state.feedingDelay--;
            if (state.cleanup++ >= 40) {
                pruneDeadFedAnimals(world);
                state.cleanup = 0;
            }
            if (state.chestSearchCooldown > 0) state.chestSearchCooldown--;

            if (state.personalChest != null) {
                BlockState blockState = world.getBlockState(state.personalChest);
                if (blockState.getBlock() != Blocks.CHEST) {
                    worldData.reservedChests.remove(state.personalChest);
                    state.personalChest = null;
                    state.chestDeposit = null;
                    worldData.markDirty();
                }
            }

            if (state.chestDeposit == null && state.chestSearchCooldown == 0) {
                BlockPos chosen = findNearestChest(villager, world);
                if (chosen != null) {
                    if (state.personalChest == null || !state.personalChest.equals(chosen)) {
                        state.personalChest = chosen;
                        worldData.reservedChests.add(chosen);
                        ((ServerWorld) world).spawnParticles(ParticleTypes.HAPPY_VILLAGER, villager.getX(), villager.getY() + 1.0, villager.getZ(), 5, 0.3, 0.3, 0.3, 0.05);
                        worldData.markDirty();
                    }
                    state.chestDeposit = chosen;
                }
                state.chestSearchCooldown = 100;
            }

            if ((time >= 8500 && time < 9000 && state.chestDepositDelay == 0) || state.hasFlag(ButcherHelper.ButcherState.FLAG_DEPOSITING)) {
                if (!state.hasFlag(ButcherHelper.ButcherState.FLAG_DEPOSITING)) {
                    state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSITING, true);
                    villager.getNavigation().stop();
                }
                if (state.chestDeposit != null) {
                    depositItemsToChest(villager, world, state.chestDeposit);
                }
                return;
            }

            if (state.groupButcheringDelay > 0) {
                state.groupButcheringDelay--;
            } else {
                state.groupButcheringDelay = 100;
                refillButcherTargets(villager, world);
            }

            if (!state.butcherTargets.isEmpty()) {
                if (world.getTime() % 20 == 0) {
                    processButcherTargets(villager, world);
                }
            } else if (state.activeTarget == null) {
                if (world.getTime() % 100 == 0) {
                    processFeedingInteraction(villager, world);
                }
            }

            processAnimalChasing(villager, world);
        }
    }

    // Gestión de Estructuras

    @Unique
    private void assignStructures(VillagerEntity villager, ServerWorld world) {
        UUID villagerId = villager.getUuid();
        GlobalWorldData worldData = GlobalWorldData.get(world);

        if (state.pen != null && state.stable != null &&
                worldData.claimedStructures.getOrDefault(state.pen, villagerId).equals(villagerId) &&
                worldData.claimedStructures.getOrDefault(state.stable, villagerId).equals(villagerId)) {
            LOGGER.info("Reutilizando estructuras para aldeano {}: pen={}, stable={}", villager.getName().getString(), state.pen, state.stable);
            return;
        }

        BlockPos villagerPos = villager.getBlockPos();
        ChunkPos chunkPos = new ChunkPos(villagerPos);
        StructureAccessor accessor = world.getStructureAccessor();
        List<StructureStart> starts = accessor.getStructureStarts(chunkPos, _ -> true);

        BlockPos foundPen = state.pen != null && worldData.claimedStructures.getOrDefault(state.pen, villagerId).equals(villagerId) ? state.pen : null;
        BlockPos foundStable = state.stable != null && worldData.claimedStructures.getOrDefault(state.stable, villagerId).equals(villagerId) ? state.stable : null;

        for (StructureStart start : starts) {
            Identifier villageId = world.getRegistryManager()
                    .getOrThrow(RegistryKeys.STRUCTURE)
                    .getId(start.getStructure());

            if (villageId == null || !villageId.getPath().startsWith("village")) {
                continue;
            }

            for (StructurePiece piece : start.getChildren()) {
                if (!(piece instanceof PoolStructurePiece)) continue;

                String desc = piece.toString();
                Matcher m = Pattern.compile("(minecraft:[a-z0-9_/]+)").matcher(desc);
                if (!m.find()) continue;

                Identifier templateId = Identifier.of(m.group(1));
                if (!templateId.getPath().contains("/houses/")) continue;

                BlockPos piecePos = piece.getBoundingBox().getCenter();

                if (ButcherHelper.ButcherState.ANIMAL_PENS.contains(templateId) && foundPen == null && !worldData.claimedStructures.containsKey(piecePos)) {
                    foundPen = piecePos;
                }

                if (ButcherHelper.ButcherState.STABLES.contains(templateId) && foundStable == null && !worldData.claimedStructures.containsKey(piecePos)) {
                    foundStable = piecePos;
                }

                if (foundPen != null && foundStable != null) break;
            }

            if (foundPen != null && foundStable != null) break;
        }

        if (foundPen != null && foundStable == null) {
            for (StructureStart start : starts) {
                for (StructurePiece piece : start.getChildren()) {
                    if (!(piece instanceof PoolStructurePiece)) continue;

                    String desc = piece.toString();
                    Matcher m = Pattern.compile("(minecraft:[a-z0-9_/]+)").matcher(desc);
                    if (!m.find()) continue;

                    Identifier templateId = Identifier.of(m.group(1));
                    if (ButcherHelper.ButcherState.ANIMAL_PENS.contains(templateId)) {
                        BlockPos candidate = piece.getBoundingBox().getCenter();
                        if (!candidate.equals(foundPen) && !worldData.claimedStructures.containsKey(candidate)) {
                            foundStable = candidate;
                            break;
                        }
                    }
                }
                if (foundStable != null) break;
            }
        } else if (foundStable != null && foundPen == null) {
            for (StructureStart start : starts) {
                for (StructurePiece piece : start.getChildren()) {
                    if (!(piece instanceof PoolStructurePiece)) continue;

                    String desc = piece.toString();
                    Matcher m = Pattern.compile("(minecraft:[a-z0-9_/]+)").matcher(desc);
                    if (!m.find()) continue;

                    Identifier templateId = Identifier.of(m.group(1));
                    if (ButcherHelper.ButcherState.STABLES.contains(templateId)) {
                        BlockPos candidate = piece.getBoundingBox().getCenter();
                        if (!candidate.equals(foundStable) && !worldData.claimedStructures.containsKey(candidate)) {
                            foundPen = candidate;
                            break;
                        }
                    }
                }
                if (foundPen != null) break;
            }
        }

        boolean modified = false;

        if (foundPen != null) {
            worldData.claimedStructures.put(foundPen, villagerId);
            manageStructurePosition("pen", foundPen);
            LOGGER.info("PEN asignado para aldeano {} en: {}", villager.getName().getString(), foundPen);
            modified = true;
        } else {
            LOGGER.warn("No se encontró PEN libre para aldeano {}", villager.getName().getString());
        }

        if (foundStable != null) {
            worldData.claimedStructures.put(foundStable, villagerId);
            manageStructurePosition("stable", foundStable);
            LOGGER.info("STABLE asignado para aldeano {} en: {}", villager.getName().getString(), foundStable);
            modified = true;
        } else {
            LOGGER.warn("No se encontró STABLE libre para aldeano {}", villager.getName().getString());
        }

        if (modified) {
            worldData.markDirty();
        }
    }

    @Unique
    private BlockPos manageStructurePosition(String structureType, BlockPos pos) {
        return switch (structureType) {
            case "pen" -> {
                if (pos != null) {
                    state.pen = pos;
                }
                yield state.pen;
            }
            case "stable" -> {
                if (pos != null) {
                    state.stable = pos;
                }
                yield state.stable;
            }
            default -> throw new IllegalArgumentException("Unknown structure type: " + structureType);
        };
    }

    // Rutina Diaria y Comportamiento Principal

    @Unique
    private void handleDailyRoutine(VillagerEntity villager, ServerWorld world) {
        if (!state.feedingPair.isEmpty() || !state.butcherTargets.isEmpty() || state.activeTarget != null) return;

        long dayTime = world.getTimeOfDay() % 24000;
        int newPhase = 0;
        if (dayTime >= 3000 && dayTime < 4000) newPhase = 1;
        else if (dayTime >= 4000 && dayTime < 5000) newPhase = 2;
        else if (dayTime >= 5000 && dayTime < 8000) newPhase = 3;

        if (newPhase != state.routinePhase) {
            LOGGER.info("Aldeano {} cambió a fase de rutina: {}", villager.getName().getString(), newPhase);
            state.routinePhase = newPhase;
        }
        if (state.routinePhase == 0) return;

        BlockPos target;

        switch (state.routinePhase) {
            case 1 -> {
                target = state.pen;
                Optional<GlobalPos> jobMem = villager.getBrain().getOptionalMemory(MemoryModuleType.JOB_SITE);
                state.savedJobPosition = (jobMem != null && jobMem.isPresent()) ? jobMem.get().pos() : null;
                LOGGER.debug("Fase 1: yendo al corral {}", target);
                if (target != null && !target.isWithinDistance(villager.getBlockPos(), 2.0)) {
                    villager.getNavigation().startMovingTo(target.getX(), target.getY(), target.getZ(), 0.5);
                }
            }
            case 2 -> {
                target = state.stable;
                LOGGER.debug("Fase 2: yendo al establo {}", target);
                if (target != null && !target.isWithinDistance(villager.getBlockPos(), 2.0)) {
                    villager.getNavigation().startMovingTo(target.getX(), target.getY(), target.getZ(), 0.5);
                }
            }
            case 3 -> {
                target = state.savedJobPosition;
                if (target == null) {
                    Optional<GlobalPos> mem = villager.getBrain().getOptionalMemory(MemoryModuleType.JOB_SITE);
                    if (mem != null && mem.isPresent() && mem.get().dimension().equals(world.getRegistryKey())) {
                        target = mem.get().pos();
                    }
                }
                if (target != null && target.isWithinDistance(villager.getBlockPos(), 1.5)) {
                    LOGGER.debug("Fase 3: interacción con ahumador en {}", target);
                    villager.getNavigation().stop();

                    DefaultedList<ItemStack> inventory = villager.getInventory().getHeldStacks();
                    int porkCount = 0, beefCount = 0, muttonCount = 0;
                    boolean hasCoal = false;

                    for (ItemStack stack : inventory) {
                        if (stack.isOf(Items.COAL)) hasCoal = true;
                        else if (stack.isOf(Items.PORKCHOP)) porkCount += stack.getCount();
                        else if (stack.isOf(Items.BEEF)) beefCount += stack.getCount();
                        else if (stack.isOf(Items.MUTTON)) muttonCount += stack.getCount();
                    }

                    BlockEntity be = world.getBlockEntity(target);
                    if (!(be instanceof SmokerBlockEntity smoker)) {
                        LOGGER.warn("Ahumador no encontrado en {}", target);
                        return;
                    }

                    boolean dirty = false;
                    boolean skipCookingThisTick = false;

                    ItemStack cooked = smoker.getStack(2);
                    if (!cooked.isEmpty() && cooked.getCount() >= state.meatToCookCount && state.meatToCookCount > 0) {
                        addToVillagerInventory(inventory, cooked.copy());
                        smoker.setStack(2, ItemStack.EMPTY);
                        dirty = true;

                        LOGGER.info("Aldeano {} recogió {} carne cocinada del ahumador.", villager.getName().getString(), cooked.getCount());

                        int count = cooked.getCount();
                        int expOrbs = (count >= 3) ? (count / 3) : 1;
                        for (int i = 0; i < expOrbs; i++) {
                            world.spawnEntity(new ExperienceOrbEntity(world, villager.getX(), villager.getY() + 0.5, villager.getZ(), 1));
                        }

                        state.meatToCookCount = 0;
                    }

                    if (dayTime >= 7999) {
                        ItemStack cookedLate = smoker.getStack(2);
                        if (!cookedLate.isEmpty()) {
                            addToVillagerInventory(inventory, cookedLate.copy());
                            smoker.setStack(2, ItemStack.EMPTY);
                            dirty = true;
                            LOGGER.info("Aldeano {} recogió carne cocinada justo antes del anochecer.", villager.getName().getString());
                        }

                        ItemStack rawMeat = smoker.getStack(0);
                        if (!rawMeat.isEmpty()) {
                            addToVillagerInventory(inventory, rawMeat.copy());
                            smoker.setStack(0, ItemStack.EMPTY);
                            dirty = true;
                            LOGGER.info("Aldeano {} recogió carne cruda del ahumador antes del anochecer.", villager.getName().getString());
                        }

                        state.meatToCookCount = 0;
                        skipCookingThisTick = true;
                    }

                    if (!skipCookingThisTick && smoker.getStack(0).isEmpty()) {
                        if (smoker.getStack(1).isEmpty() && hasCoal) {
                            for (int i = 0; i < inventory.size(); i++) {
                                ItemStack stack = inventory.get(i);
                                if (stack.isOf(Items.COAL)) {
                                    smoker.setStack(1, stack);
                                    inventory.set(i, ItemStack.EMPTY);
                                    dirty = true;
                                    LOGGER.info("Aldeano {} colocó carbón en el ahumador.", villager.getName().getString());
                                    break;
                                }
                            }
                        }

                        if (state.meatToCookCount == 0) {
                            Item itemToCook = null;
                            int count = 0;
                            if (porkCount >= beefCount && porkCount >= muttonCount && porkCount > 0) {
                                itemToCook = Items.PORKCHOP;
                                count = porkCount;
                            } else if (beefCount >= muttonCount && beefCount > 0) {
                                itemToCook = Items.BEEF;
                                count = beefCount;
                            } else if (muttonCount > 0) {
                                itemToCook = Items.MUTTON;
                                count = muttonCount;
                            }

                            if (itemToCook != null) {
                                for (int i = 0; i < inventory.size(); i++) {
                                    ItemStack stack = inventory.get(i);
                                    if (stack.isOf(itemToCook)) {
                                        inventory.set(i, ItemStack.EMPTY);
                                    }
                                }
                                smoker.setStack(0, new ItemStack(itemToCook, count));
                                state.meatToCookCount = count;
                                dirty = true;
                                LOGGER.info("Aldeano {} colocó {} unidades de {} en el ahumador.", villager.getName().getString(), count, itemToCook.getName().getString());
                            }
                        }
                    }

                    if (dirty) {
                        smoker.markDirty();
                        world.updateListeners(target, world.getBlockState(target), world.getBlockState(target), 3);
                    }

                    BlockState blockState = world.getBlockState(target);
                    boolean smokerLit = blockState.get(Properties.LIT);
                    ItemStack mainHandStack = villager.getStackInHand(Hand.MAIN_HAND);

                    if (smokerLit && mainHandStack.isEmpty()) {
                        villager.getLookControl().lookAt(Vec3d.ofCenter(target));
                        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);
                    }
                } else if (target != null) {
                    LOGGER.debug("Fase 3: yendo al ahumador {}", target);
                    villager.getNavigation().startMovingTo(target.getX(), target.getY(), target.getZ(), 0.5);
                }
            }
        }
    }


    @Unique
    private void processFeedingInteraction(VillagerEntity villager, World world) {
        if (state.activeTarget != null || state.feedingDelay > 0) return;

        if ((state.feedingPair.isEmpty() || state.feedingPair.size() < 2) && (world.getTimeOfDay() % 20 == 0)) {
            Box feedBox = villager.getBoundingBox().expand(6.0);
            long now = world.getTime();
            List<AnimalEntity> feedCandidates = world.getEntitiesByClass(
                    AnimalEntity.class,
                    feedBox,
                    entity -> {
                        if (state.fedAnimals.contains(entity)) return false;
                        boolean okType = entity instanceof CowEntity || entity instanceof PigEntity || entity instanceof SheepEntity;
                        if (!okType || entity.isBaby() || entity.getLoveTicks() > 0) return false;
                        if (entity instanceof AnimalEntityInterface access && access.aiVillagersFabric$isVillageAnimal()) {
                            UUID uuid = entity.getUuid();
                            if (state.lastFeedTimes.containsKey(uuid) && now - state.lastFeedTimes.get(uuid) < 6000L) {
                                return false;
                            }
                            state.fedAnimals.remove(entity);
                            return true;
                        }
                        return false;
                    }
            );

            Map<Class<? extends AnimalEntity>, List<AnimalEntity>> grouped = new HashMap<>();
            for (AnimalEntity a : feedCandidates) {
                grouped.computeIfAbsent(a.getClass(), _ -> new ArrayList<>()).add(a);
            }

            for (Map.Entry<Class<? extends AnimalEntity>, List<AnimalEntity>> e : grouped.entrySet()) {
                List<AnimalEntity> list = e.getValue();
                if (list.size() < 2) continue;

                Item req = null;
                if (e.getKey() == PigEntity.class) {
                    if (adjustInventory(villager, Items.CARROT, 2, InventoryOp.CHECK)) {
                        req = Items.CARROT;
                    }
                    else if (adjustInventory(villager, Items.BEETROOT, 2, InventoryOp.CHECK)) {
                        req = Items.BEETROOT;
                    }
                    else if (adjustInventory(villager, Items.POTATO, 2, InventoryOp.CHECK)) {
                        req = Items.POTATO;
                    }
                    if (req == null) continue;
                } else {
                    req = Items.WHEAT;
                    if (!adjustInventory(villager, req, 2, InventoryOp.CHECK)) continue;
                }

                list.sort(Comparator.comparingDouble(villager::squaredDistanceTo));
                state.feedingPair.clear();
                state.feedingPair.add(list.get(0));
                state.feedingPair.add(list.get(1));
                state.feedingFood = req;
                state.feedingStep = 0;
                state.feedingObservationTimer = 0;
                LOGGER.info("Aldeano {} seleccionó pareja para alimentar ({}) con {}", villager.getName().getString(), list.size(), req.getName().getString());
                LOGGER.info("Pareja: {} y {}", list.get(0).getName().getString(), list.get(1).getName().getString());
                return;
            }

            LOGGER.info("Aldeano {} no encontró pareja para alimentar.", villager.getName().getString());
        }

        if (state.feedingPair.size() == 2) {
            AnimalEntity target = state.feedingPair.get(state.feedingStep);
            if (!target.isAlive() || target.isBaby() || target.getLoveTicks() > 0
                    || !adjustInventory(villager, state.feedingFood, 1, InventoryOp.CHECK)) {
                LOGGER.warn("Alimentación cancelada: condiciones no válidas para {}", target.getName().getString());
                resetFeedingProcess();
                return;
            }

            villager.getNavigation().startMovingTo(target, 0.5);
            if (villager.squaredDistanceTo(target) <= 4.0) {
                villager.getNavigation().stop();
                villager.getLookControl().lookAt(target.getX(), target.getBodyY(0.5D), target.getZ());
                villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);
                villager.setStackInHand(Hand.MAIN_HAND, new ItemStack(state.feedingFood));
                state.feedingObservationTimer++;

                if (state.feedingObservationTimer >= 40) {
                    adjustInventory(villager, state.feedingFood, 1, InventoryOp.REMOVE);
                    if (world instanceof ServerWorld serverWorld) {
                        serverWorld.spawnParticles(
                                ParticleTypes.HEART,
                                target.getX(), target.getBodyY(0.5D), target.getZ(),
                                7, 0.5, 0.5, 0.5, 0.1
                        );
                    }
                    target.setLoveTicks(600);
                    villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
                    state.lastFeedTimes.put(target.getUuid(), world.getTime());
                    state.fedAnimals.add(target);
                    state.feedingStep++;
                    state.feedingObservationTimer = 0;
                    LOGGER.info("Aldeano {} alimentó a {} (paso {}).", villager.getName().getString(), target.getName().getString(), state.feedingStep);

                    if (state.feedingStep >= 2) {
                        LOGGER.info("Alimentación completada para pareja seleccionada.");
                        resetFeedingProcess();
                    }
                }
            }
        } else {
            state.feedingObservationTimer = 0;
        }
    }

    @Unique
    private void resetFeedingProcess() {
        state.feedingPair.clear();
        state.feedingStep = 0;
        state.feedingObservationTimer = 0;
        state.feedingDelay = 20;
        state.freeze = 120;
        LOGGER.info("Proceso de alimentación reiniciado para aldeano.");
    }

    @Unique
    private void refillButcherTargets(VillagerEntity villager, World world) {
        List<Class<? extends AnimalEntity>> types = List.of(CowEntity.class, SheepEntity.class, PigEntity.class);
        for (Class<? extends AnimalEntity> cls : types) {
            List<? extends AnimalEntity> raw = world.getEntitiesByClass(
                    cls,
                    villager.getBoundingBox().expand(6.0),
                    LivingEntity::isAlive
            );
            List<AnimalEntity> adults = new ArrayList<>();
            for (AnimalEntity a : raw) {
                if (!a.isBaby() && a instanceof AnimalEntityInterface access
                        && access.aiVillagersFabric$isVillageAnimal()) {
                    adults.add(a);
                }
            }
            int excess = adults.size() - 5;
            if (excess > 0) {
                int added = 0;
                for (AnimalEntity adult : adults) {
                    if (added++ >= excess) break;
                    if (state.butcherTargets.stream().noneMatch(bt -> bt.target.getUuid().equals(adult.getUuid()))) {
                        state.butcherTargets.add(new ButcherHelper(adult));
                    }
                }
                LOGGER.info("Aldeano {} encontró exceso de animales y añadió {} objetivos para sacrificio.", villager.getName().getString(), excess);
            }
        }
    }

    @Unique
    private void processButcherTargets(VillagerEntity villager, World world) {
        if (state.postKill > 0) {
            state.postKill--;
            return;
        }

        if (state.butcherTargets.isEmpty()) return;

        ButcherHelper butcherTarget = state.butcherTargets.getFirst();
        if (butcherTarget == null || butcherTarget.target == null) {
            state.butcherTargets.removeFirst();
            return;
        }
        AnimalEntity animalEntity = butcherTarget.target;

        if (!animalEntity.isAlive()) {
            state.butcherTargets.removeFirst();
            state.postKill = 60;
            LOGGER.debug("Aldeano {} eliminó un objetivo de sacrificio muerto.", villager.getName().getString());
            return;
        }

        villager.getLookControl().lookAt(animalEntity.getX(), animalEntity.getEyeY(), animalEntity.getZ());
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);
        villager.setStackInHand(Hand.MAIN_HAND, new ItemStack(getAxeItemForLevel()));
        villager.getNavigation().startMovingTo(animalEntity, 0.5);

        double distSq = villager.squaredDistanceTo(animalEntity);
        if (distSq <= 4.0) {
            villager.getNavigation().stop();
            animalEntity.getNavigation().stop();
            butcherTarget.lookTimer++;
            if (butcherTarget.lookTimer >= 40) {
                if (world instanceof ServerWorld serverWorld) {
                    animalEntity.damage(serverWorld, serverWorld.getDamageSources().generic(), animalEntity.getHealth());
                }
                villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
                animalEntity.getNavigation().stop();
                state.butcherTargets.removeFirst();
                state.freeze = 80;
                state.postKill = 40;
                LOGGER.info("Aldeano {} ha sacrificado a {}.", villager.getName().getString(), animalEntity.getName().getString());
            }
        } else {
            butcherTarget.lookTimer = 0;
        }
    }

    @Unique
    private void pruneDeadFedAnimals(World world) {
        long now = world.getTime();
        synchronized (state.fedAnimals) {
            state.fedAnimals.removeIf(animal -> !animal.isAlive());
        }
        synchronized (state.lastFeedTimes) {
            state.lastFeedTimes.entrySet().removeIf(entry -> now - entry.getValue() >= 6000L);
        }
        LOGGER.debug("Animales alimentados limpiados para aldeano.");
    }

    @Unique
    private void depositItemsToChest(VillagerEntity villager, World world, BlockPos chestPos) {
        GlobalWorldData worldData = GlobalWorldData.get((ServerWorld) world);
        Vec3d vec3d = new Vec3d(chestPos.getX() + 0.5, chestPos.getY() + 0.5, chestPos.getZ() + 0.5);
        if (villager.squaredDistanceTo(vec3d) > 4.0) {
            villager.getNavigation().startMovingTo(vec3d.x, vec3d.y, vec3d.z, 0.4);
            return;
        }
        villager.getNavigation().stop();
        villager.getLookControl().lookAt(vec3d.x, vec3d.y, vec3d.z);
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);
        if (!state.hasFlag(ButcherHelper.ButcherState.FLAG_CHEST_OPEN)) {
            if (world instanceof ServerWorld sw) sw.addSyncedBlockEvent(chestPos, Blocks.CHEST, 1, 1);
            world.playSound(null, vec3d.x, vec3d.y, vec3d.z, SoundEvents.BLOCK_CHEST_OPEN, SoundCategory.BLOCKS, 0.5F, 1F);
            state.setFlag(ButcherHelper.ButcherState.FLAG_CHEST_OPEN, true);
            state.chestAnimationTimer = 0;
            LOGGER.info("Aldeano {} abrió cofre en: {}.", villager.getName().getString(), chestPos);
            return;
        }
        if (!state.hasFlag(ButcherHelper.ButcherState.FLAG_DEPOSIT_COMPLETED)) {
            state.chestAnimationTimer++;
            if (state.chestAnimationTimer >= 40) {
                BlockEntity be = world.getBlockEntity(chestPos);
                if (be instanceof ChestBlockEntity chest) depositInventory(villager, chest, world);
                state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSIT_COMPLETED, true);
                state.chestAnimationTimer = 0;
                LOGGER.info("Aldeano {} depositó ítems en cofre en: {}.", villager.getName().getString(), chestPos);
            }
            return;
        }
        state.chestAnimationTimer++;
        if (state.chestAnimationTimer >= 40) {
            if (world instanceof ServerWorld sw) sw.addSyncedBlockEvent(chestPos, Blocks.CHEST, 1, 0);
            world.playSound(null, vec3d.x, vec3d.y, vec3d.z, SoundEvents.BLOCK_CHEST_CLOSE, SoundCategory.BLOCKS, 0.5F, 1F);
            worldData.reservedChests.remove(chestPos);
            state.setFlag(ButcherHelper.ButcherState.FLAG_CHEST_OPEN, false);
            state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSIT_COMPLETED, false);
            state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSITING, false);
            state.chestDeposit = null;
            state.chestAnimationTimer = 0;
            state.chestDepositDelay = 2400;
            worldData.markDirty();
            LOGGER.info("Aldeano {} cerró cofre en: {}.", villager.getName().getString(), chestPos);
        }
    }

    @Unique
    private void catchItemsFromChest(VillagerEntity villager, World world, BlockPos chestPos) {
        Vec3d vec3d = new Vec3d(chestPos.getX() + 0.5, chestPos.getY() + 0.5, chestPos.getZ() + 0.5);
        if (villager.squaredDistanceTo(vec3d) > 4.0) {
            villager.getNavigation().startMovingTo(vec3d.x, vec3d.y, vec3d.z, 0.4);
            LOGGER.info("Aldeano {} moviéndose hacia cofre en {} para retirar trigo y zanahorias.", villager.getName().getString(), chestPos);
            return;
        }
        villager.getNavigation().stop();
        villager.getLookControl().lookAt(vec3d.x, vec3d.y, vec3d.z);
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);

        if (!state.hasFlag(ButcherHelper.ButcherState.FLAG_CHEST_OPEN)) {
            if (world instanceof ServerWorld sw) sw.addSyncedBlockEvent(chestPos, Blocks.CHEST, 1, 1);
            world.playSound(null, vec3d.x, vec3d.y, vec3d.z, SoundEvents.BLOCK_CHEST_OPEN, SoundCategory.BLOCKS, 0.5F, 1F);
            state.setFlag(ButcherHelper.ButcherState.FLAG_CHEST_OPEN, true);
            state.chestAnimationTimer = 0;
            LOGGER.info("Aldeano {} abrió cofre en {} para retirar trigo y zanahorias.", villager.getName().getString(), chestPos);
            return;
        }

        if (!state.hasFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWN)) {
            state.chestAnimationTimer++;
            if (state.chestAnimationTimer >= 40) {
                BlockEntity be = world.getBlockEntity(chestPos);
                if (be instanceof ChestBlockEntity chest) {
                    int wheatToTake = 0;
                    int carrotToTake = 0;
                    for (int i = 0; i < chest.size(); i++) {
                        ItemStack stack = chest.getStack(i);
                        if (stack.isOf(Items.WHEAT)) {
                            wheatToTake += stack.getCount();
                            stack.setCount(0);
                        } else if (stack.isOf(Items.CARROT)) {
                            carrotToTake += stack.getCount();
                            stack.setCount(0);
                        }
                    }

                    int wheatForVillager = Math.min(wheatToTake, 8);
                    int wheatToReturn = wheatToTake - wheatForVillager;
                    int carrotForVillager = Math.min(carrotToTake, 8);
                    int carrotToReturn = carrotToTake - carrotForVillager;

                    if (wheatForVillager > 0) {
                        adjustInventory(villager, Items.WHEAT, wheatForVillager, InventoryOp.ADD);
                        LOGGER.info("Aldeano {} retiró {} trigo del cofre en {}.", villager.getName().getString(), wheatForVillager, chestPos);
                    }
                    if (carrotForVillager > 0) {
                        adjustInventory(villager, Items.CARROT, carrotForVillager, InventoryOp.ADD);
                        LOGGER.info("Aldeano {} retiró {} zanahorias del cofre en {}.", villager.getName().getString(), carrotForVillager, chestPos);
                    }

                    if (wheatToReturn > 0) {
                        ItemStack remainingWheat = new ItemStack(Items.WHEAT, wheatToReturn);
                        for (int i = 0; i < chest.size(); i++) {
                            if (chest.getStack(i).isEmpty()) {
                                chest.setStack(i, remainingWheat);
                                break;
                            }
                        }
                        LOGGER.info("Aldeano {} dejó {} trigo en el cofre en {}.", villager.getName().getString(), wheatToReturn, chestPos);
                    }
                    if (carrotToReturn > 0) {
                        ItemStack remainingCarrot = new ItemStack(Items.CARROT, carrotToReturn);
                        for (int i = 0; i < chest.size(); i++) {
                            if (chest.getStack(i).isEmpty()) {
                                chest.setStack(i, remainingCarrot);
                                break;
                            }
                        }
                        LOGGER.info("Aldeano {} dejó {} zanahorias en el cofre en {}.", villager.getName().getString(), carrotToReturn, chestPos);
                    }

                    chest.markDirty();
                    world.updateListeners(chestPos, world.getBlockState(chestPos), world.getBlockState(chestPos), 3);
                }
                state.setFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWN, true);
                state.chestAnimationTimer = 0;
                LOGGER.info("Aldeano {} completó retiro de trigo y zanahorias en cofre en {}.", villager.getName().getString(), chestPos);
            }
            return;
        }

        state.chestAnimationTimer++;
        if (state.chestAnimationTimer >= 40) {
            if (world instanceof ServerWorld sw) sw.addSyncedBlockEvent(chestPos, Blocks.CHEST, 1, 0);
            world.playSound(null, vec3d.x, vec3d.y, vec3d.z, SoundEvents.BLOCK_CHEST_CLOSE, SoundCategory.BLOCKS, 0.5F, 1F);
            state.setFlag(ButcherHelper.ButcherState.FLAG_CHEST_OPEN, false);
            state.setFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWN, false);
            state.setFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWING, false);
            LOGGER.info("Aldeano {} cerró cofre en {} tras retirar trigo y zanahorias.", villager.getName().getString(), chestPos);
        }
    }

    // Interacciones con Animales

    @Unique
    private void processAnimalChasing(VillagerEntity villager, World world) {
        if (world.getTimeOfDay() % 20 != 0) return;
        ItemStack hand = villager.getStackInHand(Hand.MAIN_HAND);
        if (hand.isEmpty()) return;
        Item held = hand.getItem();
        Box chaseBox = villager.getBoundingBox().expand(6.0);

        if (held == Items.CARROT || held == Items.BEETROOT || held == Items.POTATO) {
            List<PigEntity> pigs = world.getEntitiesByClass(PigEntity.class, chaseBox, PigEntity::isAlive);
            for (PigEntity pig : pigs) {
                if (pig.squaredDistanceTo(villager) > 4.0) {
                    pig.getNavigation().startMovingTo(villager, 1.2);
                    pig.getLookControl().lookAt(villager.getX(), villager.getBodyY(0.5D), villager.getZ());
                    pig.getBrain().forget(MemoryModuleType.LOOK_TARGET);
                } else {
                    pig.getNavigation().stop();
                }
            }
        } else if (held == Items.WHEAT) {
            List<AnimalEntity> list = world.getEntitiesByClass(
                    AnimalEntity.class,
                    chaseBox,
                    e -> (e instanceof CowEntity || e instanceof SheepEntity) && e.isAlive()
            );
            for (AnimalEntity animalEntity : list) {
                if (animalEntity.squaredDistanceTo(villager) > 4.0) {
                    animalEntity.getNavigation().startMovingTo(villager, 1.1);
                    animalEntity.getLookControl().lookAt(villager.getX(), villager.getBodyY(0.5D), villager.getZ());
                    animalEntity.getBrain().forget(MemoryModuleType.LOOK_TARGET);
                } else {
                    animalEntity.getNavigation().stop();
                }
            }
        }
    }

    // Gestión del Inventario

    @Unique
    private void manageInventory(VillagerEntity villager, String action, List<ItemStack> items) {
        switch (action) {
            case "add":
                if (items != null) {
                    for (ItemStack item : items) {
                        villager.getInventory().addStack(item);
                    }
                }
                break;
            case "clear":
                villager.getInventory().clear();
                villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
                break;
            default:
                LOGGER.error("Acción de inventario desconocida para aldeano {}: {}", villager.getName().getString(), action);
                throw new IllegalArgumentException("Unknown inventory action: " + action);
        }
    }

    @Unique
    private void addToVillagerInventory(DefaultedList<ItemStack> inventory, ItemStack stackToAdd) {
        for (ItemStack slot : inventory) {
            if (!slot.isEmpty()
                    && ItemStack.areItemsAndComponentsEqual(slot, stackToAdd)
                    && slot.getCount() < slot.getMaxCount()) {

                int space = slot.getMaxCount() - slot.getCount();
                int move = Math.min(space, stackToAdd.getCount());
                slot.increment(move);
                stackToAdd.decrement(move);
                if (stackToAdd.isEmpty()) return;
            }
        }

        for (int i = 0; i < inventory.size(); i++) {
            if (inventory.get(i).isEmpty()) {
                inventory.set(i, stackToAdd.copy());
                return;
            }
        }
    }

    @Unique
    private void resetStatePreservingCooldowns() {
        state.feedingPair.clear();
        state.feedingStep = 0;
        state.feedingObservationTimer = 0;
        state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSITING, false);
        state.chestDeposit = null;
        state.setFlag(ButcherHelper.ButcherState.FLAG_CHEST_OPEN, false);
        state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSIT_COMPLETED, false);
        state.chestAnimationTimer = 0;
    }

    @Unique
    private void clearAxesFromInventory(VillagerEntity villager) {
        adjustInventory(villager, Items.WOODEN_AXE, 1, InventoryOp.REMOVE);
        adjustInventory(villager, Items.STONE_AXE, 1, InventoryOp.REMOVE);
        adjustInventory(villager, Items.IRON_AXE, 1, InventoryOp.REMOVE);
        adjustInventory(villager, Items.GOLDEN_AXE, 1, InventoryOp.REMOVE);
        adjustInventory(villager, Items.DIAMOND_AXE, 1, InventoryOp.REMOVE);
    }

    @Unique
    private void ensureAxeForLevel(VillagerEntity villager) {
        Item desiredAxe = getAxeItemForLevel();
        if (!adjustInventory(villager, desiredAxe, 1, InventoryOp.CHECK)) {
            clearAxesFromInventory(villager);
            adjustInventory(villager, desiredAxe, 1, InventoryOp.ADD);
            LOGGER.info("Aldeano {} recibió hacha de nivel: {}.", villager.getName().getString(), desiredAxe);
        }
    }

    @Unique
    private Item getAxeItemForLevel() {
        return switch (state.butcherLevel) {
            case 2 -> Items.STONE_AXE;
            case 3 -> Items.GOLDEN_AXE;
            case 4 -> Items.IRON_AXE;
            case 5 -> Items.DIAMOND_AXE;
            default -> Items.WOODEN_AXE;
        };
    }

    @Unique
    private void depositInventory(VillagerEntity villager, Inventory chestInventory, World world) {
        List<Integer> empty = new ArrayList<>();
        for (int j = 0; j < chestInventory.size(); j++) {
            if (chestInventory.getStack(j).isEmpty()) empty.add(j);
        }
        if (empty.isEmpty()) return;
        for (int i = 0; i < villager.getInventory().size(); i++) {
            var vs = villager.getInventory().getStack(i);
            if (!vs.isEmpty()
                    && vs.getItem() != Items.WOODEN_AXE
                    && vs.getItem() != Items.STONE_AXE
                    && vs.getItem() != Items.IRON_AXE
                    && vs.getItem() != Items.GOLDEN_AXE
                    && vs.getItem() != Items.DIAMOND_AXE) {
                if (empty.isEmpty()) break;
                int idx = world.random.nextInt(empty.size());
                int slot = empty.remove(idx);
                chestInventory.setStack(slot, vs.copy());
                villager.getInventory().setStack(i, ItemStack.EMPTY);
            }
        }
    }

    @Unique
    private boolean adjustInventory(VillagerEntity villagerEntity, Item item, int amount, InventoryOp inventoryOp) {
        int remainingAmount = amount;

        for (int i = 0; i < villagerEntity.getInventory().size(); i++) {
            var stack = villagerEntity.getInventory().getStack(i);
            if (inventoryOp == InventoryOp.ADD) {
                if (stack.isEmpty()) {
                    villagerEntity.getInventory().setStack(i, new ItemStack(item, remainingAmount));
                    return true;
                } else if (stack.getItem() == item && stack.getCount() < stack.getMaxCount()) {
                    int canAdd = stack.getMaxCount() - stack.getCount();
                    int toAdd = Math.min(canAdd, remainingAmount);
                    stack.increment(toAdd);
                    remainingAmount -= toAdd;
                    if (remainingAmount <= 0) return true;
                }
            } else if (!stack.isEmpty() && stack.getItem() == item) {
                if (inventoryOp == InventoryOp.CHECK) {
                    remainingAmount -= stack.getCount();
                    if (remainingAmount <= 0) return true;
                } else if (inventoryOp == InventoryOp.REMOVE) {
                    int toRemove = Math.min(stack.getCount(), remainingAmount);
                    stack.decrement(toRemove);
                    remainingAmount -= toRemove;
                    if (remainingAmount <= 0) return true;
                }
            }
        }
        return remainingAmount <= 0;
    }

    // Utilidades Generales

    @Unique
    private BlockPos findNearestChest(VillagerEntity villager, World world) {
        GlobalWorldData worldData = GlobalWorldData.get((ServerWorld) world);
        if (state.personalChest != null) {
            LOGGER.info("Usando cofre personal cacheado: {}", state.personalChest);
            return state.personalChest;
        }

        worldData.reservedChests.removeIf(pos -> world.getBlockState(pos).getBlock() != Blocks.CHEST);
        if (!worldData.reservedChests.isEmpty()) {
            worldData.markDirty();
        }

        Optional<GlobalPos> bedPosOptional = villager.getBrain().getOptionalMemory(MemoryModuleType.HOME);
        assert bedPosOptional != null;
        if (bedPosOptional.isEmpty()) {
            LOGGER.warn("Aldeano {} no tiene cama asignada.", villager.getName().getString());
            return null;
        }

        BlockPos center = bedPosOptional.get().pos();
        BlockPos nearest = null;
        double minDist = Double.MAX_VALUE;
        int rXZ = 48;
        int rY = 6;

        LOGGER.debug("Buscando cofres cercanos desde {}", center);
        
        for (int radius = 1; radius <= rXZ && nearest == null; radius++) {
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dy = -rY; dy <= rY; dy++) {
                    for (int dz = -radius; dz <= radius; dz++) {
                        if (Math.abs(dx) != radius && Math.abs(dz) != radius) continue;

                        BlockPos p = center.add(dx, dy, dz);
                        if (world.getBlockState(p).getBlock() == Blocks.CHEST) {
                            LOGGER.debug("Cofre detectado en {}", p);
                            if (!isChestClaimedByOtherVillager(villager, p, world)) {
                                double dist = new Vec3d(p.getX() + .5, p.getY() + .5, p.getZ() + .5)
                                        .squaredDistanceTo(new Vec3d(center.getX() + .5, center.getY() + .5, center.getZ() + .5));
                                if (dist < minDist) {
                                    nearest = p;
                                    minDist = dist;
                                    LOGGER.debug("Cofre en {} es el más cercano hasta ahora (distancia={})", p, dist);
                                }
                            }
                        }
                    }
                }
            }
        }
        if (nearest != null) {
            LOGGER.debug("Cofre más cercano encontrado en {} a distancia {}", nearest, Math.sqrt(minDist));
        } else {
            LOGGER.debug("No se encontró ningún cofre disponible cerca de {}", center);
        }
        return nearest;
    }

    @Unique
    private boolean isChestClaimedByOtherVillager(VillagerEntity villager, BlockPos chestPos, World world) {
        UUID villagerId = villager.getUuid();
        GlobalWorldData worldData = GlobalWorldData.get((ServerWorld) world);
        Box searchBox = villager.getBoundingBox().expand(16.0);

        List<VillagerEntity> villagers = world.getEntitiesByClass(
                VillagerEntity.class,
                searchBox,
                v -> !v.getUuid().equals(villagerId) && v instanceof ButcherHelper.ButcherAccess
        );

        for (VillagerEntity other : villagers) {
            ButcherHelper.ButcherState otherState = ((ButcherHelper.ButcherAccess) other).aiVillagersFabric$getState();
            if (otherState != null && otherState.chestDeposit != null && otherState.chestDeposit.equals(chestPos)) {
                LOGGER.info("Cofre en {} ya está reclamado por aldeano {}", chestPos, other.getName().getString());
                return true;
            }
        }

        boolean isReserved = worldData.reservedChests.contains(chestPos);
        if (isReserved) {
            LOGGER.info("Cofre en {} está reservado por otro aldeano (cached)", chestPos);
        }

        return isReserved;
    }

    // Métodos de Interfaz

    @Override
    public Map<UUID, Long> aiVillagersFabric$getLastFeedingTimestamps() {
        return state.lastFeedTimes;
    }

    @Override
    public List<AnimalEntity> aiVillagersFabric$getFedAnimals() {
        return state.fedAnimals;
    }

    @Override
    public ButcherHelper.ButcherState aiVillagersFabric$getState() {
        return state;
    }

    // Variables de persistencia

    @Inject(method = "writeCustomData", at = @At("TAIL"))
    private void onWriteCustomData(WriteView view, CallbackInfo ci) {
        if (state.feedingPair == null) {
            state.feedingPair = new ArrayList<>();
        }

        view.putInt("butcherFreeze", state.freeze);
        view.putInt("butcherPostKillDelay", state.postKill);
        view.putInt("butcherTickCounter", state.butcherTickCounter);
        view.putInt("butcherMeatToCookCount", state.meatToCookCount);
        view.putInt("butcherFeedingDelay", state.feedingDelay);
        view.putInt("butcherChestDepositDelay", state.chestDepositDelay);
        view.putInt("butcherFeedingStep", state.feedingStep);
        view.putInt("butcherGroupDelay", state.groupButcheringDelay);
        view.putInt("butcherRoutinePhase", state.routinePhase);
        view.putInt("butcherProfessionLossTimer", state.professionLossTimer);
        view.putInt("butcherLevel", state.butcherLevel);

        view.putBoolean("butcherDepositing", state.hasFlag(ButcherHelper.ButcherState.FLAG_DEPOSITING));
        view.putBoolean("ButcherStructuresAssigned", state.hasFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_ASSIGNED));
        view.putBoolean("hasReceivedButcherBonus", state.hasFlag(ButcherHelper.ButcherState.FLAG_HAS_BONUS));
        view.putBoolean("wasButcherItem", state.hasFlag(ButcherHelper.ButcherState.FLAG_WAS_BUTCHER_ITEM));
        view.putBoolean("receivedInitialItems", state.hasFlag(ButcherHelper.ButcherState.FLAG_RECEIVED_INITIAL_ITEMS));
        view.putBoolean("butcherItemWithdrawing", state.hasFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWING));
        view.putBoolean("butcherItemWithdrawn", state.hasFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWN));
        view.putBoolean("butcherStructuresReleased", state.hasFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_RELEASED));

        view.putString("butcherPhase", state.phase.name());

        if (state.chestDeposit != null) {
            view.putInt("butcherDepositX", state.chestDeposit.getX());
            view.putInt("butcherDepositY", state.chestDeposit.getY());
            view.putInt("butcherDepositZ", state.chestDeposit.getZ());
        }

        if (state.savedJobPosition != null) {
            view.putInt("SavedJobPosX", state.savedJobPosition.getX());
            view.putInt("SavedJobPosY", state.savedJobPosition.getY());
            view.putInt("SavedJobPosZ", state.savedJobPosition.getZ());
        }

        if (state.personalChest != null) {
            view.putInt("PersonalChestX", state.personalChest.getX());
            view.putInt("PersonalChestY", state.personalChest.getY());
            view.putInt("PersonalChestZ", state.personalChest.getZ());
        }

        if (state.pen != null) view.putLong(ButcherHelper.ButcherState.NBT_KEY_PEN, state.pen.asLong());
        if (state.stable != null) view.putLong(ButcherHelper.ButcherState.NBT_KEY_STABLE, state.stable.asLong());

        if (state.feedingFood != null) {
            view.putString("butcherFeedingFood", Registries.ITEM.getId(state.feedingFood).toString());
        }

        var tickAppender = view.getListAppender("butcherLastFeedTicks", ButcherHelper.FEEDING_TICK_CODEC);
        state.lastFeedTimes.forEach((uuid, t) -> tickAppender.add(new ButcherHelper.FeedingTick(uuid.toString(), t)));

        var targetAppender = view.getListAppender("butcherTargets", Codec.compoundList(Codec.STRING, Codec.INT));
        for (ButcherHelper bt : state.butcherTargets) {
            targetAppender.add(Collections.singletonList(Pair.of(bt.target.getUuid().toString(), bt.lookTimer)));
        }

        if (state.activeTarget != null) {
            view.putString("butcherActiveTarget", state.activeTarget.getUuid().toString());
        }

        var pairAppender = view.getListAppender("butcherFeedingPair", Codec.STRING);
        for (AnimalEntity e : state.feedingPair) {
            pairAppender.add(e.getUuid().toString());
        }
    }

    @Inject(method = "readCustomData", at = @At("TAIL"))
    private void onReadCustomData(ReadView view, CallbackInfo ci) {

        state.freeze = view.getInt("butcherFreeze", 0);
        state.postKill = view.getInt("butcherPostKillDelay", 0);
        state.butcherTickCounter = view.getInt("butcherTickCounter", 0);
        state.meatToCookCount = view.getInt("butcherMeatToCookCount", 0);
        state.feedingDelay = view.getInt("butcherFeedingDelay", 0);
        state.chestDepositDelay = view.getInt("butcherChestDepositDelay", 0);
        state.feedingStep = view.getInt("butcherFeedingStep", 0);
        state.groupButcheringDelay = view.getInt("butcherGroupDelay", 100);
        state.routinePhase = view.getInt("butcherRoutinePhase", 0);
        state.professionLossTimer = view.getInt("butcherProfessionLossTimer", 0);
        state.butcherLevel = view.getInt("butcherLevel", 1);

        state.setFlag(ButcherHelper.ButcherState.FLAG_DEPOSITING, view.getBoolean("butcherDepositing", false));
        state.setFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_ASSIGNED, view.getBoolean("ButcherStructuresAssigned", false));
        state.setFlag(ButcherHelper.ButcherState.FLAG_HAS_BONUS, view.getBoolean("hasReceivedButcherBonus", false));
        state.setFlag(ButcherHelper.ButcherState.FLAG_WAS_BUTCHER_ITEM, view.getBoolean("wasButcherItem", false));
        state.setFlag(ButcherHelper.ButcherState.FLAG_RECEIVED_INITIAL_ITEMS, view.getBoolean("receivedInitialItems", false));
        state.setFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWING, view.getBoolean("butcherItemWithdrawing", false));
        state.setFlag(ButcherHelper.ButcherState.FLAG_ITEM_WITHDRAWN, view.getBoolean("butcherItemWithdrawn", false));
        state.setFlag(ButcherHelper.ButcherState.FLAG_STRUCTURES_RELEASED, view.getBoolean("butcherStructuresReleased", false));

        view.getOptionalString("butcherPhase").ifPresent(phaseName -> {
            try {
                state.phase = ButcherHelper.ButcherState.Phase.valueOf(phaseName);
            } catch (IllegalArgumentException e) {
                state.phase = ButcherHelper.ButcherState.Phase.IDLE;
            }
        });

        Optional<Integer> ox = view.getOptionalInt("butcherDepositX");
        Optional<Integer> oy = view.getOptionalInt("butcherDepositY");
        Optional<Integer> oz = view.getOptionalInt("butcherDepositZ");
        state.chestDeposit = (ox.isPresent() && oy.isPresent() && oz.isPresent())
                ? new BlockPos(ox.get(), oy.get(), oz.get())
                : null;

        view.getOptionalInt("SavedJobPosX").ifPresent(x -> {
            int y = view.getInt("SavedJobPosY", 0);
            int z = view.getInt("SavedJobPosZ", 0);
            state.savedJobPosition = new BlockPos(x, y, z);
        });

        view.getOptionalInt("PersonalChestX").ifPresent(x -> {
            int y = view.getInt("PersonalChestY", 0);
            int z = view.getInt("PersonalChestZ", 0);
            state.personalChest = new BlockPos(x, y, z);
        });

        state.pen = view.getOptionalLong(ButcherHelper.ButcherState.NBT_KEY_PEN).map(BlockPos::fromLong).orElse(null);
        state.stable = view.getOptionalLong(ButcherHelper.ButcherState.NBT_KEY_STABLE).map(BlockPos::fromLong).orElse(null);

        view.getOptionalString("butcherFeedingFood")
                .ifPresent(id -> state.feedingFood = Registries.ITEM.get(Identifier.of(id)));

        state.lastFeedTimes.clear();
        view.getOptionalListReadView("butcherLastFeedTicks").ifPresent(list -> {
            for (ReadView e : list) {
                e.getOptionalString("uuid").ifPresent(uuidStr ->
                        e.getOptionalLong("lastFeedTick").ifPresent(t -> {
                            try {
                                state.lastFeedTimes.put(UUID.fromString(uuidStr), t);
                            } catch (IllegalArgumentException ignored) {}
                        })
                );
            }
        });

        state.fedAnimals.clear();
        long now = this.getWorld().getTime();
        Map<UUID, Long> feedTimesCopy = new HashMap<>(state.lastFeedTimes);
        for (Map.Entry<UUID, Long> entry : feedTimesCopy.entrySet()) {
            UUID uuid = entry.getKey();
            long lastFeed = entry.getValue();
            if (now - lastFeed < 6000L) {
                Entity e = this.getWorld().getEntity(uuid);
                if (e instanceof AnimalEntity a && a.isAlive()) {
                    state.fedAnimals.add(a);
                }
            }
        }

        state.butcherTargets.clear();
        view.getOptionalListReadView("butcherTargets").ifPresent(list -> {
            for (ReadView e : list) {
                e.getOptionalString("uuid").ifPresent(uuidStr -> {
                    try {
                        UUID uuid = UUID.fromString(uuidStr);
                        int look = e.getInt("lookTimer", 0);
                        Entity entity = getWorld().getEntity(uuid);
                        if (entity instanceof AnimalEntity ent && ent.isAlive()) {
                            ButcherHelper bt = new ButcherHelper(ent);
                            bt.lookTimer = look;
                            state.butcherTargets.add(bt);
                        }
                    } catch (IllegalArgumentException ignored) {}
                });
            }
        });

        view.getOptionalString("butcherActiveTarget").ifPresent(uuidStr -> {
            try {
                Entity entity = getWorld().getEntity(UUID.fromString(uuidStr));
                if (entity instanceof AnimalEntity animal && animal.isAlive()) {
                    state.activeTarget = animal;
                } else {
                    state.activeTarget = null;
                }
            } catch (IllegalArgumentException e) {
                state.activeTarget = null;
            }
        });

        state.feedingPair.clear();
        view.getOptionalTypedListView("butcherFeedingPair", Codec.STRING).ifPresent(list -> {
            for (String uuidStr : list) {
                try {
                    Entity entity = getWorld().getEntity(UUID.fromString(uuidStr));
                    if (entity instanceof AnimalEntity ent && ent.isAlive()) {
                        state.feedingPair.add(ent);
                    }
                } catch (IllegalArgumentException ignored) {}
            }
        });
    }
}