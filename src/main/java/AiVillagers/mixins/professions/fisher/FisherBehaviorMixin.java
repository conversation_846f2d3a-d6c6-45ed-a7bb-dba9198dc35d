package AiVillagers.mixins.professions.fisher;

import AiVillagers.data.FisherWorldData;
import AiVillagers.entities.fishing.VillagerFishingBobberEntity;
import AiVillagers.interfaces.AiTaskInterruptHandler;
import AiVillagers.interfaces.FisherHelper;
import AiVillagers.interfaces.VillagerFishHookInterface;
import AiVillagers.items.ModItems;
import net.minecraft.block.entity.BlockEntity;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.ai.brain.MemoryModuleType;
import net.minecraft.entity.mob.MobEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.inventory.Inventory;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.tag.FluidTags;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundEvents;
import net.minecraft.storage.ReadView;
import net.minecraft.storage.WriteView;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.math.*;
import net.minecraft.village.VillagerProfession;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import static AiVillagers.core.AiVillagersMod.LOGGER;

@Mixin(VillagerEntity.class)
public abstract class FisherBehaviorMixin extends MobEntity implements AiTaskInterruptHandler {

    @Unique private final FisherHelper.FisherState state = new FisherHelper.FisherState();

    protected FisherBehaviorMixin(EntityType<? extends MobEntity> entityType, World world) {
        super(entityType, world);
    }

    @Inject(method = "interactMob(Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/util/Hand;)Lnet/minecraft/util/ActionResult;",
            at = @At("HEAD"), cancellable = true)
    private void onPlayerInteractionAttempt(PlayerEntity player, Hand hand, CallbackInfoReturnable<ActionResult> cir) {
        if (state.currentFishingTarget == null) return;

        VillagerEntity villager = (VillagerEntity) (Object) this;
        long currentTimeOfDay = this.getWorld().getTimeOfDay() % 24000;

        if (currentTimeOfDay < 8500) {

            villager.playSound(SoundEvents.ENTITY_VILLAGER_NO, 1.0F, 1.0F);
            villager.setHeadRollingTimeLeft(40);
            LOGGER.info("Pescador {} rechazó comercio durante horas de trabajo (tiempo: {})", villager.getName().getString(), currentTimeOfDay);

            cir.setReturnValue(ActionResult.PASS);
        } else {
            LOGGER.info("Pescador {} permite comercio durante horas libres (tiempo: {})",
                villager.getName().getString(), currentTimeOfDay);
        }
    }
    
    @Inject(method = "tick", at = @At("HEAD"))
    private void processFisherVillagerBehavior(final CallbackInfo ci) {
        if (this.getWorld().isClient) return;

        final var fisherVillagerEntity = (VillagerEntity)(Object)this;
        final var totalWorldTimeElapsed = this.getWorld().getTimeOfDay();
        final var currentDayTimeInTicks = totalWorldTimeElapsed % 24000;
        final var currentDayNumber = totalWorldTimeElapsed / 24000;

        processBarrelWorkstationInteractionStateMachine(currentDayTimeInTicks, currentDayNumber, fisherVillagerEntity);

        if (executeInitialValidationsAndMaintenanceTasks(fisherVillagerEntity, currentDayTimeInTicks)) return;

        processFishingStateMachine(fisherVillagerEntity);
    }
    
    @Unique
    private boolean executeInitialValidationsAndMaintenanceTasks(final VillagerEntity fisherVillagerEntity, final long currentDayTimeInTicks) {
        if (executePostWorldReloadHandCleanupProtocol(fisherVillagerEntity)) return true;
        if (handleEmergencyWaterEscape(fisherVillagerEntity)) return true;

        executePerformanceOptimizedCacheCleanup();

        if (!verifyAndEnforceFishermanProfessionRequirements(fisherVillagerEntity)) return true;

        if (currentDayTimeInTicks >= 8500 && currentDayTimeInTicks < 8540) {
            fisherVillagerEntity.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
        }

        if ((currentDayTimeInTicks >= 8500 && currentDayTimeInTicks < 24000) || fisherVillagerEntity.isSleeping()) {
            executeNightTimeAndSleepingStateCleanup(fisherVillagerEntity);
            return true;
        }

        return false;
    }
    
    @Unique
    private void executePerformanceOptimizedCacheCleanup() {
        final var data = retrieveGlobalFishingDataManager();
        if (data != null) {
            final var currentTime = this.getWorld().getTime();
            data.cleanupOldData(currentTime, 24000);
            data.cleanupExpiredReservedSpots(currentTime);
        }

        final var currentTime = this.getWorld().getTime();

        final var expiredThreshold = currentTime - 1200;
        state.spotsCacheTimestamp.entrySet().removeIf(entry -> entry.getValue() < expiredThreshold);
        state.validSpotsCache.keySet().removeIf(key -> !state.spotsCacheTimestamp.containsKey(key));
    }

    @Unique
    private void executeNightTimeAndSleepingStateCleanup(final VillagerEntity villager) {
        if (state.isCurrentlyFishing || state.currentFishingTarget != null) {
            LOGGER.info("Pescador {} terminando pesca por tiempo nocturno/sueño", villager.getName().getString());
            if (state.currentFishingTarget != null) {
                releaseSpotGlobally(state.currentFishingTarget);
            }
            executeComprehensiveFishingStateResetAndCleanup(villager);
        }
    }
    
    @Unique
    private void processFishingStateMachine(final VillagerEntity fisherVillagerEntity) {
        if (processFishingCooldownTimer(fisherVillagerEntity)) return;
        if (processActiveFishingSequenceAndBobberManagement(fisherVillagerEntity)) return;
        if (executeNavigationToFishingSpotTarget(fisherVillagerEntity)) return;

        initiateFishingSpotDiscoveryAndSelection(fisherVillagerEntity);
    }
    
    @Unique
    private boolean verifyAndEnforceFishermanProfessionRequirements(final VillagerEntity villager) {
        Objects.requireNonNull(villager, "Villager cannot be null for profession validation");

        if (hasValidFishermanProfessionCredentials(villager)) {
            return true;
        }

        if (state.isCurrentlyFishing || state.currentFishingTarget != null) {
            LOGGER.info("Pescador {} perdió su profesión - terminando pesca activa",
                villager.getName().getString());

            if (state.currentFishingTarget != null) {
                releaseSpotGlobally(state.currentFishingTarget);
            }

            executeComprehensiveFishingStateResetAndCleanup(villager);
            state.isCurrentlyFishing = false;
            state.currentFishingTarget = null;
        }

        final var currentTime = this.getWorld().getTime();
        if ((currentTime & 199) == 0) {
            final var profession = villager.getVillagerData().profession().value().toString();
            LOGGER.debug("Aldeano {} no es pescador: {}", villager.getName().getString(), profession);
        }

        return false;
    }

    @Unique
    private boolean executePostWorldReloadHandCleanupProtocol(final VillagerEntity villager) {
        if (state.needsHandCleanup && state.handCleanupTimer > 0) {
            state.handCleanupTimer--;

            villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);

            if (state.handCleanupTimer % 20 == 0) {
                int secondsRemaining = state.handCleanupTimer / 20;
                LOGGER.info("Pescador {} limpiando mano después de recargar mundo - {} segundos restantes",
                    villager.getName().getString(), secondsRemaining);
            }

            if (state.handCleanupTimer == 0) {
                state.needsHandCleanup = false;
                LOGGER.info("Pescador {} completó limpieza de mano después de recargar mundo",
                    villager.getName().getString());
            }

            return true;
        }
        return false;
    }

    @Unique
    private boolean processFishingCooldownTimer(final VillagerEntity villager) {
        if (state.fishingCooldownTimer > 0) {
            state.fishingCooldownTimer--;
            if (state.fishingCooldownTimer % 200 == 0) {
                LOGGER.debug("Pescador {} en cooldown: {} ticks restantes",
                    villager.getName().getString(), state.fishingCooldownTimer);
            }
            return true;
        }
        return false;
    }

    @Unique
    private boolean processActiveFishingSequenceAndBobberManagement(final VillagerEntity villager) {
        if (state.isCurrentlyFishing && state.currentFishingTarget != null) {
            haltMovementAndPerformFishing(villager);
            return true;
        }
        return false;
    }

    @Unique
    private boolean executeNavigationToFishingSpotTarget(final VillagerEntity villager) {
        if (state.currentFishingTarget != null) {
            double distance = this.getBlockPos().getSquaredDistance(state.currentFishingTarget);

            if (distance <= 1) {
                this.getNavigation().stop();

                if (!state.isCurrentlyFishing) {
                    initiateFishingSequence(villager);
                    LOGGER.info("Pescador {} llegó al spot {} y comenzó a pescar inmediatamente",
                            villager.getName().getString(), state.currentFishingTarget);
                }
            } else {
                this.getNavigation().startMovingTo(state.currentFishingTarget.getX(), state.currentFishingTarget.getY(), state.currentFishingTarget.getZ(), 0.5);
            }
            return true;
        }
        return false;
    }

    @Unique
    private FisherWorldData retrieveGlobalFishingDataManager() {
        if (this.getWorld() instanceof ServerWorld serverWorld) {
            return FisherWorldData.get(serverWorld);
        }
        return null;
    }

    @Unique
    private boolean checkIfFishingSpotIsGloballyBlacklisted(final BlockPos fishingSpotPosition) {
        FisherWorldData data = retrieveGlobalFishingDataManager();
        if (data == null) return false;

        long currentTime = this.getWorld().getTime();
        return data.isSpotFailed(fishingSpotPosition, currentTime, 24000);
    }

    @Unique
    private void markSpotAsFailedGlobally(BlockPos pos) {
        FisherWorldData data = retrieveGlobalFishingDataManager();
        if (data != null) {
            data.addFailedSpot(pos, this.getWorld().getTime());
            data.limitCacheSize(50);
        }
    }

    @Unique
    private boolean reserveSpotGlobally(BlockPos pos) {
        FisherWorldData data = retrieveGlobalFishingDataManager();
        if (data == null) return false;

        if (data.isSpotReserved(pos)) {
            LOGGER.info("Pescador {} no pudo reservar spot {} - ya está reservado",
                this.getName().getString(), pos);
            return false;
        }

        long currentGameTime = this.getWorld().getTime();
        data.reserveSpot(pos, currentGameTime);
        LOGGER.info("Pescador {} reservó exitosamente spot {} (se liberará automáticamente en 20 segundos)",
            this.getName().getString(), pos);
        return true;
    }

    @Unique
    private void releaseSpotGlobally(BlockPos pos) {
        FisherWorldData data = retrieveGlobalFishingDataManager();
        if (data != null) {
            data.releaseSpot(pos);
            LOGGER.info("Pescador {} liberó spot {}",
                this.getName().getString(), pos);
        }
    }
    
    @Unique
    private void initiateFishingSpotDiscoveryAndSelection(final VillagerEntity villager) {
        if (!state.isCurrentlyFishing && state.fishingCooldownTimer == 0 && state.currentFishingTarget == null) {
            long currentTime = this.getWorld().getTime();
            if (currentTime - state.lastSpotSearchTime >= 20) {
                LOGGER.info("Pescador {} iniciando búsqueda de spot de pesca", villager.getName().getString());
                executeComprehensiveFishingSpotAnalysisAndSelection(villager);
                state.lastSpotSearchTime = currentTime;
            }
        }
    }

    @Unique
    private boolean handleEmergencyWaterEscape(VillagerEntity villager) {
        if (this.getWorld().getBlockState(this.getBlockPos()).getFluidState().isIn(FluidTags.WATER) && state.isCurrentlyFishing) {
            state.fishingCooldownTimer = calculateExperienceBasedAdaptiveFishingCooldownDuration();
            if (state.currentFishingTarget != null) {
                releaseSpotGlobally(state.currentFishingTarget);
            }
            executeComprehensiveFishingStateResetAndCleanup(villager);
            return true;
        }
        return false;
    }

    @Unique
    private static boolean hasValidFishermanProfessionCredentials(final VillagerEntity villager) {
        final var profession = Objects.requireNonNull(villager, "Villager cannot be null").getVillagerData().profession();
        return profession.matchesKey(VillagerProfession.FISHERMAN);
    }

    @Unique
    private void processBarrelWorkstationInteractionStateMachine(final long timeOfDay, final long currentDay, final VillagerEntity villager) {
        long currentGameTime = this.getWorld().getTime();

        if (state.personalBarrelWorkTime == -1) {
            int[] timeOptions = {8500, 8600, 8700, 8800, 8900};
            state.personalBarrelWorkTime = timeOptions[this.getRandom().nextInt(timeOptions.length)];
            LOGGER.info("Pescador {} asignado tiempo de trabajo en barril: {}", villager.getName().getString(), state.personalBarrelWorkTime);
        }

        long workWindowEnd = Math.min(state.personalBarrelWorkTime + 200, 9000);
        boolean shouldWork = timeOfDay >= state.personalBarrelWorkTime && timeOfDay < workWindowEnd && !state.isStoringItemsInBarrel && state.lastWorkSessionTime != currentDay && currentGameTime - state.lastWorkSessionTime >= 2400;

        if (shouldWork) {
            if (state.lastItemCheckDay != currentDay) {
                state.lastItemCheckDay = currentDay;
                state.hasItemsForBarrel = validateVillagerInventoryContainsFishingRelatedItems(villager);
                LOGGER.info("Pescador {} verificó items antes de ir al barril - {}", villager.getName().getString(), state.hasItemsForBarrel ? "SÍ tiene items" : "NO tiene items");
            }

            if (state.hasItemsForBarrel) {
                if (state.isCurrentlyFishing || state.currentFishingTarget != null) {
                    LOGGER.info("Pescador {} terminando pesca para trabajar en barril", villager.getName().getString());
                    if (state.currentFishingTarget != null) {
                        releaseSpotGlobally(state.currentFishingTarget);
                    }
                    executeComprehensiveFishingStateResetAndCleanup(villager);
                    state.isCurrentlyFishing = false;
                    state.currentFishingTarget = null;
                }

                state.lastWorkSessionTime = currentGameTime;
                Optional<GlobalPos> jobSite = villager.getBrain().getOptionalMemory(MemoryModuleType.JOB_SITE);
                assert jobSite != null;
                if (jobSite.isPresent() && jobSite.get().dimension() == this.getWorld().getRegistryKey()) {
                    state.assignedWorkStation = jobSite.get().pos();
                    LOGGER.info("Pescador {} iniciando trabajo en barril: {}", villager.getName().getString(), state.assignedWorkStation);
                }
            }
        }

        if (state.assignedWorkStation != null) {
            if (!this.getBlockPos().isWithinDistance(state.assignedWorkStation, 2.0)) {
                this.getNavigation().startMovingTo(state.assignedWorkStation.getX(),
                    state.assignedWorkStation.getY(), state.assignedWorkStation.getZ(), 0.5);
            } else {
                this.getNavigation().stop();
                villager.getLookControl().lookAt(state.assignedWorkStation.getX() + 0.5, state.assignedWorkStation.getY() + 0.5, state.assignedWorkStation.getZ() + 0.5);
                villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);

                if (!state.isStoringItemsInBarrel) {
                    state.isStoringItemsInBarrel = true;
                    state.barrelInteractionTimer = 0;
                    getWorld().playSound(null, state.assignedWorkStation, SoundEvents.BLOCK_BARREL_OPEN,
                        this.getSoundCategory(), 1.0f, 1.0f);
                    storeItemsInWorkStationBarrel(state.assignedWorkStation);
                    LOGGER.info("Pescador iniciando almacenamiento en barril: {}", state.assignedWorkStation);
                } else {
                    state.barrelInteractionTimer++;
                    if (state.barrelInteractionTimer >= 40) {
                        getWorld().playSound(null, state.assignedWorkStation, SoundEvents.BLOCK_BARREL_CLOSE, this.getSoundCategory(), 1.0f, 1.0f);
                        state.isStoringItemsInBarrel = false;
                        state.barrelInteractionTimer = 0;
                        state.assignedWorkStation = null;
                        LOGGER.info("Pescador completó almacenamiento en barril");
                    }
                }
            }
        }
    }

    @Unique
    private boolean validateVillagerInventoryContainsFishingRelatedItems(final VillagerEntity fisherVillagerEntity) {
        for (int slotIndex = 0; slotIndex < fisherVillagerEntity.getInventory().size(); slotIndex++) {
            ItemStack currentStack = fisherVillagerEntity.getInventory().getStack(slotIndex);
            if (!currentStack.isEmpty() && isFishingRelatedItem(currentStack)) {
                return true;
            }
        }
        return false;
    }

    @Unique
    private void haltMovementAndPerformFishing(VillagerEntity villager) {
        this.getNavigation().stop();
        this.setVelocity(0, this.getVelocity().y, 0);

        villager.setStackInHand(Hand.MAIN_HAND, new ItemStack(ModItems.VILLAGER_FISHING_ROD_CAST));

        VillagerFishingBobberEntity myBobber = getMyOwnBobber(villager);
        if (myBobber != null && !myBobber.isRemoved()) {
            if (!validateBobberInWater(villager, myBobber)) {
                return;
            }

            Vec3d bobberPos = myBobber.getPos();
            villager.getLookControl().lookAt(bobberPos.x, bobberPos.y, bobberPos.z);
        } else {
            villager.getLookControl().lookAt(state.currentFishingTarget.getX() + 0.5, state.currentFishingTarget.getY() + 0.5, state.currentFishingTarget.getZ() + 0.5);
        }
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);

        state.fishingLookTimer++;

        int completionTime = state.willFishingSucceed ?
                state.fishingEventTime + 20 :
                state.fishingEventTime + state.animationDuration + 20;

        if (state.fishingLookTimer >= completionTime) {
            completeFishingAttempt(villager);
        } else if (state.fishingLookTimer == state.fishingEventTime && myBobber != null && !state.fishingEventTriggered) {
            triggerFishingEvent(villager, myBobber);
        }
    }

    @Unique
    private void triggerFishingEvent(VillagerEntity villager, VillagerFishingBobberEntity myBobber) {
        if (myBobber != null && !myBobber.isRemoved() && !state.fishingEventTriggered) {
            state.fishingEventTriggered = true;
            myBobber.initiateFishingEvent();
            if (state.willFishingSucceed) {
                LOGGER.info("Pescador {} - Iniciando evento de pesca EXITOSA en {}s",
                    villager.getName().getString(), state.fishingLookTimer / 20.0);
            } else {
                LOGGER.info("Pescador {} - Iniciando evento de pesca FALLIDA en {}s",
                    villager.getName().getString(), state.fishingLookTimer / 20.0);
            }
        }
    }

    @Unique
    private VillagerFishingBobberEntity getMyOwnBobber(VillagerEntity villager) {
        if (state.villagerBobber != null && !state.villagerBobber.isRemoved()) {
            VillagerEntity bobberOwner = state.villagerBobber.getBobberOwner();
            if (bobberOwner != null && bobberOwner.equals(villager)) {
                return state.villagerBobber;
            }
        }

        if (villager instanceof VillagerFishHookInterface fishHookInterface) {
            VillagerFishingBobberEntity myBobber = fishHookInterface.aiVillagersFabric$getFishHook();
            if (myBobber != null && !myBobber.isRemoved()) {
                VillagerEntity bobberOwner = myBobber.getBobberOwner();
                if (bobberOwner != null && bobberOwner.equals(villager)) {
                    state.villagerBobber = myBobber;
                    return myBobber;
                }
            }
        }

        return null;
    }

    @Unique
    private boolean validateBobberInWater(VillagerEntity villager, VillagerFishingBobberEntity myBobber) {
        if (myBobber == null || myBobber.isRemoved()) {
            LOGGER.warn("Pescador {} - Bobber perdido, terminando pesca", villager.getName().getString());
            failFishingAttempt(villager, "bobber perdido");
            return false;
        }

        if (state.fishingLookTimer < 40) {
            return true;
        }


        if (!state.willFishingSucceed && state.fishingEventTriggered) {
            int timeAfterEvent = state.fishingLookTimer - state.fishingEventTime;

            if (timeAfterEvent > 0 && timeAfterEvent < state.animationDuration) {
                return true;
            }
        }

        if (!state.willFishingSucceed && state.fishingLookTimer >= state.fishingEventTime && state.fishingEventTime > 0) {
            return true;
        }

        BlockPos bobberPos = myBobber.getBlockPos();
        World world = this.getWorld();

        boolean isInWater = world.getBlockState(bobberPos).getFluidState().isIn(FluidTags.WATER);
        boolean isAboveWater = !isInWater && world.getBlockState(bobberPos.down()).getFluidState().isIn(FluidTags.WATER);

        if (!isInWater && !isAboveWater) {
            LOGGER.warn("Pescador {} - Bobber no está en agua después de 2s (posición: {}, bloque: {}, bloque abajo: {}), terminando pesca",
                villager.getName().getString(), bobberPos, world.getBlockState(bobberPos).getBlock(), world.getBlockState(bobberPos.down()).getBlock());
            failFishingAttempt(villager, "bobber no en agua");
            return false;
        }

        return true;
    }

    @Unique
    private void failFishingAttempt(VillagerEntity villager, String reason) {
        LOGGER.info("Pescador {} - Pesca fallida por: {}", villager.getName().getString(), reason);

        if (state.currentFishingTarget != null) {
            markSpotAsFailedGlobally(state.currentFishingTarget);
            LOGGER.info("Pescador {} - Spot {} marcado como fallido",
                villager.getName().getString(), state.currentFishingTarget);
        }

        state.consecutiveFailures++;
        state.consecutiveSuccesses = 0;

        releaseSpotGlobally(state.currentFishingTarget);
        executeComprehensiveFishingStateResetAndCleanup(villager);

        state.fishingCooldownTimer = 100;

        LOGGER.info("Pescador {} - Cooldown fijo aplicado: 100 ticks por fallo de bobber ({})",
            villager.getName().getString(), reason);
    }

    @Unique
    private void completeFishingAttempt(VillagerEntity villager) {
        boolean fishingSuccess = state.fishingResultPredetermined && state.willFishingSucceed;
        state.lastFishingAttemptTime = this.getWorld().getTime();

        if (fishingSuccess) {
            state.consecutiveSuccesses++;
            state.consecutiveFailures = 0;
            awardRandomFishingLoot(villager);
            villager.playSound(SoundEvents.ENTITY_VILLAGER_YES, 1.0F, 1.0F);
            LOGGER.info("Pescador {} tuvo ÉXITO pescando en {} (éxitos consecutivos: {}, tiempo total: {}s)",
                    villager.getName().getString(), state.currentFishingTarget, state.consecutiveSuccesses, state.fishingLookTimer / 20.0);
        } else {
            state.consecutiveFailures++;
            villager.playSound(SoundEvents.ENTITY_VILLAGER_NO, 1.0F, 1.0F);
            markSpotAsFailedGlobally(state.currentFishingTarget);
            LOGGER.info("Pescador {} FALLÓ pescando en {} (fallos consecutivos: {}, éxitos acumulados: {}, tiempo total: {}s)",
                    villager.getName().getString(), state.currentFishingTarget, state.consecutiveFailures, state.consecutiveSuccesses, state.fishingLookTimer / 20.0);
        }

        releaseSpotGlobally(state.currentFishingTarget);
        executeComprehensiveFishingStateResetAndCleanup(villager);
        state.fishingCooldownTimer = calculateExperienceBasedAdaptiveFishingCooldownDuration();
    }

    @Unique
    private void executeComprehensiveFishingSpotAnalysisAndSelection(final VillagerEntity fisherVillagerEntity) {
        BlockPos villagerPosition = this.getBlockPos();

        if (tryReuseLastFishingSpot(villagerPosition, fisherVillagerEntity)) {
            return;
        }

        BlockPos newSpot = locateOptimalFishingSpot(villagerPosition);
        if (newSpot != null && reserveSpotGlobally(newSpot)) {
            state.currentFishingTarget = newSpot;
            if (!checkIfFishingSpotIsGloballyBlacklisted(newSpot)) {
                state.lastSuccessfulFishingSpot = newSpot;
            }

            LOGGER.info("Pescador {} encontró nuevo spot de pesca: {}",
                fisherVillagerEntity.getName().getString(), newSpot);
        } else {
            state.fishingCooldownTimer = calculateExperienceBasedAdaptiveFishingCooldownDuration();
            if (this.getWorld().getTime() % 100 == 0) {
                LOGGER.info("Pescador {} no encontró spot válido, aplicando cooldown",
                    fisherVillagerEntity.getName().getString());
            }
        }
    }

    @Unique
    private boolean tryReuseLastFishingSpot(BlockPos currentPosition, VillagerEntity villager) {
        if (state.lastSuccessfulFishingSpot != null
            && currentPosition.isWithinDistance(state.lastSuccessfulFishingSpot, 10)
            && !checkIfFishingSpotIsGloballyBlacklisted(state.lastSuccessfulFishingSpot)
            && reserveSpotGlobally(state.lastSuccessfulFishingSpot)) {

            state.currentFishingTarget = state.lastSuccessfulFishingSpot;
            LOGGER.info("Pescador {} reutilizando spot exitoso: {}",
                villager.getName().getString(), state.lastSuccessfulFishingSpot);
            return true;
        }
        return false;
    }

    @Unique
    private void initiateFishingSequence(VillagerEntity villager) {
        this.getNavigation().stop();

        BlockPos baseWaterPosition = findNearestWaterPosition(state.currentFishingTarget);
        if (baseWaterPosition == null) {
            LOGGER.warn("No se encontró posición de agua válida cerca de {}", state.currentFishingTarget);
            state.fishingCooldownTimer = calculateExperienceBasedAdaptiveFishingCooldownDuration();
            return;
        }

        state.targetWaterPosition = findOptimalBobberPosition(villager, baseWaterPosition);

        assert state.targetWaterPosition != null;
        Vec3d exactTargetPos = new Vec3d(state.targetWaterPosition.getX() + 0.5, state.targetWaterPosition.getY() + 0.1, state.targetWaterPosition.getZ() + 0.5);

        villager.getLookControl().lookAt(exactTargetPos.x, exactTargetPos.y, exactTargetPos.z);
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);

        LOGGER.info("Pescador {} orientado hacia target exacto: ({}, {}, {}) desde posición: ({}, {}, {})",
            villager.getName().getString(),
            String.format("%.2f", exactTargetPos.x), String.format("%.2f", exactTargetPos.y), String.format("%.2f", exactTargetPos.z),
            String.format("%.2f", villager.getX()), String.format("%.2f", villager.getY()), String.format("%.2f", villager.getZ()));

        state.isCurrentlyFishing = true;
        state.fishingLookTimer = 0;

        villager.setStackInHand(Hand.MAIN_HAND, new ItemStack(ModItems.VILLAGER_FISHING_ROD_CAST));

        predetermineFishingResult();

        try {
            executeVillagerBobberCleanupWithOptionalAudioFeedback(false);

            Vec3d targetPos = new Vec3d(state.targetWaterPosition.getX() + 0.5, state.targetWaterPosition.getY() + 0.1, state.targetWaterPosition.getZ() + 0.5);

            state.villagerBobber = new VillagerFishingBobberEntity(villager.getWorld(), villager, targetPos);
            if (villager.getWorld().spawnEntity(state.villagerBobber)) {
                state.villagerBobber.configureFishingOutcome(state.willFishingSucceed, state.fishingEventTime);
                villager.getWorld().playSound(null, villager.getBlockPos(), SoundEvents.ENTITY_FISHING_BOBBER_THROW,
                    villager.getSoundCategory(), 1.0F, 1.0F);
                LOGGER.info("Bobber personalizado creado para aldeano {} hacia {} (resultado: {}, evento: {}s)",
                    villager.getName().getString(), state.targetWaterPosition,
                    state.willFishingSucceed ? "ÉXITO" : "FALLO", state.fishingEventTime / 20.0);
            } else {
                LOGGER.warn("Falló al spawnear bobber para aldeano {}", villager.getName().getString());
                state.villagerBobber = null;
            }
        } catch (Exception e) {
            LOGGER.error("Error creando bobber para pescador {}: {}", villager.getName().getString(), e.getMessage());
            state.villagerBobber = null;
        }

        LOGGER.info("Pescador {} comenzó secuencia de pesca en posición: {} hacia agua: {} (resultado: {}, evento en: {}s)",
                villager.getName().getString(), state.currentFishingTarget, state.targetWaterPosition,
                state.willFishingSucceed ? "ÉXITO" : "FALLO", state.fishingEventTime / 20.0);
    }

    @Unique
    private void predetermineFishingResult() {
        double currentFailureRate = 0.65;
        currentFailureRate -= (state.consecutiveSuccesses * 0.02);
        currentFailureRate = Math.max(0.4, currentFailureRate);

        state.willFishingSucceed = this.getRandom().nextDouble() > currentFailureRate;
        state.fishingResultPredetermined = true;

        if (state.willFishingSucceed) {
            state.fishingEventTime = 300 + this.getRandom().nextInt(61);
            state.animationDuration = 20;
        } else {
            state.fishingEventTime = 260 + this.getRandom().nextInt(41);
            state.animationDuration = 80;
        }
    }

    @Unique
    private void executeVillagerBobberCleanupWithOptionalAudioFeedback(final boolean shouldPlayRetrieveSound) {
        if (state.villagerBobber != null) {
            if (!state.villagerBobber.isRemoved()) {
                if (shouldPlayRetrieveSound) {
                    VillagerEntity villager = (VillagerEntity)(Object)this;
                    if (!this.getWorld().isClient) {
                        villager.playSound(SoundEvents.ENTITY_FISHING_BOBBER_RETRIEVE, 0.8F, 1.0F);
                    }
                }
                state.villagerBobber.discard();
            }
            state.villagerBobber = null;
        }
    }

    @Unique
    private BlockPos findNearestWaterPosition(BlockPos fishingSpot) {
        World world = this.getWorld();

        for (Direction direction : Direction.Type.HORIZONTAL) {
            BlockPos waterCandidate = fishingSpot.offset(direction);
            if (world.getBlockState(waterCandidate).getFluidState().isIn(FluidTags.WATER)) {
                return waterCandidate;
            }
        }

        for (BlockPos pos : BlockPos.iterateOutwards(fishingSpot, 3, 1, 3)) {
            if (world.getBlockState(pos).getFluidState().isIn(FluidTags.WATER)) {
                return pos;
            }
        }

        return null;
    }

    @Unique
    private BlockPos findOptimalBobberPosition(VillagerEntity villager, BlockPos waterPosition) {
        World world = this.getWorld();
        BlockPos villagerPos = villager.getBlockPos();

        Vec3d direction = Vec3d.of(waterPosition.subtract(villagerPos));
        double distance = direction.length();

        if (distance < 2.0) {
            LOGGER.info("Pescador {} usando agua original {} - muy cerca (distancia: {})",
                    villager.getName().getString(), waterPosition, distance);
            return waterPosition;
        }

        Vec3d normalizedDirection = direction.normalize();
        int maxDistance = Math.min(4, (int) distance);
        BlockPos bestPosition = waterPosition;

        for (int step = 1; step <= maxDistance; step++) {
            double nextX = waterPosition.getX() + (normalizedDirection.x * step);
            double nextZ = waterPosition.getZ() + (normalizedDirection.z * step);

            BlockPos checkPos = new BlockPos((int)Math.floor(nextX), waterPosition.getY(), (int)Math.floor(nextZ));

            if (world.getBlockState(checkPos).getFluidState().isIn(FluidTags.WATER)) {
                bestPosition = checkPos;
                LOGGER.info("Pescador {} encontró agua en paso {} ({}): {} - continuando",
                        villager.getName().getString(), step, checkPos, world.getBlockState(checkPos).getBlock());
            } else {
                LOGGER.info("Pescador {} encontró bloque no-agua en paso {} ({}): {} - deteniéndose",
                        villager.getName().getString(), step, checkPos, world.getBlockState(checkPos).getBlock());
                break;
            }
        }

        return bestPosition;
    }

    @Unique
    private BlockPos locateOptimalFishingSpot(BlockPos originPosition) {
        long currentTime = this.getWorld().getTime();
        if (state.validSpotsCache.containsKey(originPosition)) {
            Long cacheTime = state.spotsCacheTimestamp.get(originPosition);
            if (cacheTime != null && (currentTime - cacheTime) < 1200) {
                List<BlockPos> cachedSpots = state.validSpotsCache.get(originPosition);
                if (!cachedSpots.isEmpty()) {
                    List<BlockPos> validCachedSpots = new ArrayList<>();
                    for (BlockPos cachedSpot : cachedSpots) {
                        if (isValidFishingSpot(cachedSpot, this.getWorld(), false)) {
                            validCachedSpots.add(cachedSpot);
                        }
                    }

                    if (!validCachedSpots.isEmpty()) {
                        state.validSpotsCache.put(originPosition, validCachedSpots);
                        BlockPos selectedSpot = validCachedSpots.get(this.getRandom().nextInt(validCachedSpots.size()));
                        LOGGER.debug("Usando spot de pesca desde cache validado: {} ({} spots válidos de {} originales)",
                            selectedSpot, validCachedSpots.size(), cachedSpots.size());
                        return selectedSpot;
                    } else {
                        LOGGER.info("Cache invalidado para posición {} - todos los spots son inválidos", originPosition);
                        state.validSpotsCache.remove(originPosition);
                        state.spotsCacheTimestamp.remove(originPosition);
                    }
                }
            }
        }

        World world = this.getWorld();
        List<BlockPos> validSpots = new ArrayList<>();

        List<BlockPos> waterPositions = new ArrayList<>();

        int maxRadius = 16;
        LOGGER.info("Buscando agua desde posición {} en radios 1-{} (adaptativo hasta 24)", originPosition, maxRadius);

        for (int radius = 1; radius <= maxRadius; radius += 2) {
            int waterFoundThisRadius = 0;
            for (BlockPos searchPos : BlockPos.iterateOutwards(originPosition, radius, 4, radius)) {
                if (world.getBlockState(searchPos).getFluidState().isIn(FluidTags.WATER)) {
                    boolean isSurfaceWater = world.getBlockState(searchPos.up()).isAir() ||
                                           world.getBlockState(searchPos.up()).getFluidState().isEmpty();
                    if (isSurfaceWater) {
                        waterPositions.add(new BlockPos(searchPos.getX(), searchPos.getY(), searchPos.getZ()));
                        waterFoundThisRadius++;
                    }
                }
            }
            LOGGER.info("Radio {}: encontrados {} bloques de agua superficial (total: {})", radius, waterFoundThisRadius, waterPositions.size());

            if (waterPositions.size() >= 10 && radius >= 6) {
                LOGGER.info("Suficiente agua encontrada ({} bloques) en radio {}, deteniendo búsqueda", waterPositions.size(), radius);
                break;
            }

            if (!waterPositions.isEmpty() && radius >= 3) break;
        }

        if (waterPositions.size() < 5) {
            LOGGER.info("Poca agua encontrada ({}), extendiendo búsqueda hasta radio 24", waterPositions.size());
            for (int radius = maxRadius + 2; radius <= 24; radius += 2) {
                int waterFoundThisRadius = 0;
                for (BlockPos searchPos : BlockPos.iterateOutwards(originPosition, radius, 4, radius)) {
                    if (world.getBlockState(searchPos).getFluidState().isIn(FluidTags.WATER)) {
                        boolean isSurfaceWater = world.getBlockState(searchPos.up()).isAir() ||
                                               world.getBlockState(searchPos.up()).getFluidState().isEmpty();
                        if (isSurfaceWater) {
                            waterPositions.add(new BlockPos(searchPos.getX(), searchPos.getY(), searchPos.getZ()));
                            waterFoundThisRadius++;
                        }
                    }
                }
                LOGGER.info("Radio extendido {}: encontrados {} bloques de agua superficial (total: {})", radius, waterFoundThisRadius, waterPositions.size());

                if (waterPositions.size() >= 8) {
                    LOGGER.info("Suficiente agua encontrada en búsqueda extendida ({}), deteniendo", waterPositions.size());
                    break;
                }
            }
        }

        LOGGER.info("Total de posiciones de agua encontradas: {}", waterPositions.size());
        for (int i = 0; i < Math.min(waterPositions.size(), 5); i++) {
            LOGGER.info("Agua {}: {}", i + 1, waterPositions.get(i));
        }

        for (BlockPos waterPos : waterPositions) {
            LOGGER.info("Evaluando agua en {} para generar spots de pesca", waterPos);

            for (Direction direction : Direction.Type.HORIZONTAL) {
                BlockPos solidGroundPos = null;
                int foundDistance = -1;

                for (int distance = 1; distance <= 5; distance++) {
                    BlockPos checkPos = waterPos.offset(direction, distance);
                    if (world.getBlockState(checkPos).isSolidBlock(world, checkPos)) {
                        solidGroundPos = checkPos;
                        foundDistance = distance;
                        LOGGER.info("Encontrado bloque sólido en dirección {} distancia {}: {} en posición {}",
                            direction, distance, world.getBlockState(checkPos).getBlock(), checkPos);
                        break;
                    }
                }

                if (solidGroundPos == null) {
                    LOGGER.info("No se encontró bloque sólido en dirección {} hasta distancia 5", direction);
                    continue;
                }

                BlockPos candidateSpot = solidGroundPos.up();

                LOGGER.info("Evaluando candidato {} (dirección {}, distancia {}) para agua {} - bloque sólido: {}",
                    candidateSpot, direction, foundDistance, waterPos, world.getBlockState(solidGroundPos).getBlock());

                if (isValidFishingSpotAtWaterLevel(candidateSpot, waterPos, world, false)) {
                    validSpots.add(candidateSpot);
                    LOGGER.info("Candidato {} ACEPTADO como spot válido - bloque sólido debajo: {}",
                        candidateSpot, world.getBlockState(solidGroundPos).getBlock());
                    if (validSpots.size() >= 5) break;
                } else {
                    LOGGER.info("Candidato {} RECHAZADO", candidateSpot);
                }
            }
            if (validSpots.size() == 5) break;
        }

        if (!validSpots.isEmpty()) {
            state.validSpotsCache.put(originPosition, new ArrayList<>(validSpots));
            state.spotsCacheTimestamp.put(originPosition, currentTime);
            BlockPos selectedSpot = validSpots.get(this.getRandom().nextInt(validSpots.size()));
            LOGGER.info("Encontrado spot de pesca: {} a nivel de agua: {}", selectedSpot, selectedSpot.getY());
            return selectedSpot;
        }

        LOGGER.info("No se encontraron spots válidos, buscando spots fallidos como último recurso...");
        List<BlockPos> failedSpots = new ArrayList<>();
        for (BlockPos waterPos : waterPositions) {
            for (Direction direction : Direction.Type.HORIZONTAL) {
                BlockPos solidGroundPos = null;

                for (int distance = 1; distance <= 5; distance++) {
                    BlockPos checkPos = waterPos.offset(direction, distance);
                    if (world.getBlockState(checkPos).isSolidBlock(world, checkPos)) {
                        solidGroundPos = checkPos;
                        break;
                    }
                }

                if (solidGroundPos == null) {
                    continue;
                }

                BlockPos candidateSpot = solidGroundPos.up();

                if (isValidFishingSpotAtWaterLevel(candidateSpot, waterPos, world, true)) {
                    failedSpots.add(candidateSpot);
                    if (failedSpots.size() >= 3) break;
                }
            }
            if (failedSpots.size() == 3) break;
        }

        if (!failedSpots.isEmpty()) {
            BlockPos selectedSpot = failedSpots.get(this.getRandom().nextInt(failedSpots.size()));
            LOGGER.info("Usando spot fallido como último recurso: {} a nivel de agua: {}", selectedSpot, selectedSpot.getY());
            return selectedSpot;
        }

        LOGGER.info("No se encontraron spots fallidos, buscando cualquier spot ignorando reservas...");
        List<BlockPos> desperateSpots = new ArrayList<>();
        for (BlockPos waterPos : waterPositions) {
            for (Direction direction : Direction.Type.HORIZONTAL) {
                BlockPos solidGroundPos = null;

                for (int distance = 1; distance <= 5; distance++) {
                    BlockPos checkPos = waterPos.offset(direction, distance);
                    if (world.getBlockState(checkPos).isSolidBlock(world, checkPos)) {
                        solidGroundPos = checkPos;
                        break;
                    }
                }

                if (solidGroundPos == null) {
                    continue;
                }

                BlockPos candidateSpot = solidGroundPos.up();

                if (isValidFishingSpotIgnoringBlacklist(candidateSpot, waterPos, world)) {
                    desperateSpots.add(candidateSpot);
                    if (desperateSpots.size() >= 2) break;
                }
            }
            if (desperateSpots.size() == 2) break;
        }

        if (!desperateSpots.isEmpty()) {
            BlockPos selectedSpot = desperateSpots.get(this.getRandom().nextInt(desperateSpots.size()));
            LOGGER.info("Usando spot desesperado ignorando reservas: {} a nivel de agua: {}", selectedSpot, selectedSpot.getY());
            return selectedSpot;
        }

        if (this.getWorld().getTime() % 100 == 0) {
            FisherWorldData data = retrieveGlobalFishingDataManager();
            int reservedCount = data != null ? data.globallyReservedSpots.size() : 0;
            LOGGER.info("No se encontró spot de pesca válido cerca de {} (spots reservados: {}, agua encontrada: {})",
                originPosition, reservedCount, waterPositions.size());
            cleanupFailedSpotsWithoutWater();
        }
        return null;
    }

    @Unique
    private boolean isValidFishingSpot(BlockPos position, World world, boolean allowFailedSpots) {
        FisherWorldData data = retrieveGlobalFishingDataManager();
        if (data != null && data.isSpotReserved(position)) return false;

        if (position.getY() < world.getSeaLevel() - 5) return false;

        if (!world.isAir(position)) return false;

        BlockPos groundPosition = position.down();
        if (!world.getBlockState(groundPosition).isSolidBlock(world, groundPosition)) return false;

        if (!allowFailedSpots && checkIfFishingSpotIsGloballyBlacklisted(position)) return false;

        for (Direction direction : Direction.Type.HORIZONTAL) {
            BlockPos adjacentPosition = position.offset(direction);
            if (world.getBlockState(adjacentPosition).getFluidState().isIn(FluidTags.WATER)) return true;
        }

        return false;
    }

    @Unique
    private boolean isValidFishingSpotIgnoringBlacklist(BlockPos position, BlockPos waterPos, World world) {

        FisherWorldData data = retrieveGlobalFishingDataManager();
        if (data != null && data.isSpotReserved(position)) return false;

        if (!world.getBlockState(position).getCollisionShape(world, position).isEmpty()) return false;

        BlockPos groundPos = position.down();
        if (!world.getBlockState(groundPos).isSolidBlock(world, groundPos)) return false;

        if (!world.getBlockState(waterPos).getFluidState().isIn(FluidTags.WATER)) return false;

        return !isObstructedBetweenFishingSpotAndWater(position, waterPos, world);
    }

    @Unique
    private boolean isValidFishingSpotAtWaterLevel(BlockPos position, BlockPos waterPos, World world, boolean allowFailedSpots) {
        FisherWorldData data = retrieveGlobalFishingDataManager();
        if (data != null && data.isSpotReserved(position)) return false;

        if (!allowFailedSpots && checkIfFishingSpotIsGloballyBlacklisted(position)) return false;

        if (!world.isAir(position)) return false;

        BlockPos groundPos = position.down();
        if (!world.getBlockState(groundPos).isSolidBlock(world, groundPos)) return false;

        if (isObstructedBetweenFishingSpotAndWater(position, waterPos, world)) return false;

        return world.getBlockState(waterPos).getFluidState().isIn(FluidTags.WATER);
    }

    @Unique
    private boolean isObstructedBetweenFishingSpotAndWater(BlockPos fishingSpot, BlockPos waterPos, World world) {
        int dx = waterPos.getX() - fishingSpot.getX();
        int dz = waterPos.getZ() - fishingSpot.getZ();

        int stepX = dx == 0 ? 0 : dx / Math.abs(dx);
        int stepZ = dz == 0 ? 0 : dz / Math.abs(dz);

        int currentX = fishingSpot.getX();
        int currentZ = fishingSpot.getZ();

        while (currentX != waterPos.getX() || currentZ != waterPos.getZ()) {
            if (currentX != waterPos.getX()) currentX += stepX;
            if (currentZ != waterPos.getZ()) currentZ += stepZ;

            BlockPos checkPos = new BlockPos(currentX, fishingSpot.getY(), currentZ);

            if (!world.getBlockState(checkPos).getCollisionShape(world, checkPos).isEmpty()) {
                return true;
            }
        }

        return false;
    }

    @Unique
    private void awardRandomFishingLoot(VillagerEntity villager) {
        ItemStack[] availableLoot = {
                new ItemStack(Items.TROPICAL_FISH),
                new ItemStack(Items.PUFFERFISH),
                new ItemStack(Items.SALMON),
                new ItemStack(Items.STRING),
                new ItemStack(Items.TRIPWIRE_HOOK),
                new ItemStack(Items.COAL),
                new ItemStack(Items.COD)
        };

        ItemStack selectedLoot = availableLoot[this.getRandom().nextInt(availableLoot.length)];
        villager.getInventory().addStack(selectedLoot);
    }

    @Unique
    private int calculateExperienceBasedAdaptiveFishingCooldownDuration() {
        int baseCooldown = FisherHelper.FisherState.FISHING_COOLDOWN_BASE_OPTIONS[this.getRandom().nextInt(FisherHelper.FisherState.FISHING_COOLDOWN_BASE_OPTIONS.length)];

        int experienceCooldown = baseCooldown - (state.consecutiveSuccesses * 20) + (state.consecutiveFailures * 40);
        experienceCooldown = Math.max(400, Math.min(640, experienceCooldown));

        double variationPercent = 0.15 + (this.getRandom().nextDouble() * 0.10);
        int variationAmount = (int) (experienceCooldown * variationPercent);

        int finalCooldown = experienceCooldown;
        if (this.getRandom().nextBoolean()) {
            finalCooldown += variationAmount;
        } else {
            finalCooldown -= variationAmount;
        }

        int extraVariation = 30 + this.getRandom().nextInt(51);
        if (this.getRandom().nextBoolean()) {
            finalCooldown += extraVariation;
        } else {
            finalCooldown -= extraVariation;
        }

        finalCooldown = Math.max(400, Math.min(640, finalCooldown));

        double finalSeconds = finalCooldown / 20.0;
        double baseSeconds = baseCooldown / 20.0;
        double experienceSeconds = experienceCooldown / 20.0;
        double diffSeconds = finalSeconds - experienceSeconds;

        String logMessage = String.format(
                "Cooldown natural: %d ticks (%.1fs) [base: %.1fs, experiencia: %.1fs, variación: %+,.1fs]",
                finalCooldown, finalSeconds, baseSeconds, experienceSeconds, diffSeconds
        );

        LOGGER.info(logMessage);

        return finalCooldown;
    }

    @Unique
    private void storeItemsInWorkStationBarrel(BlockPos barrelPosition) {
        BlockEntity blockEntity = this.getWorld().getBlockEntity(barrelPosition);
        if (!(blockEntity instanceof Inventory barrelInventory)) {
            return;
        }

        VillagerEntity villager = (VillagerEntity)(Object)this;
        List<ItemStack> itemsToStore = collectFishingItemsFromInventory(villager);

        if (itemsToStore.isEmpty()) {
            return;
        }

        storeItemsInInventory(itemsToStore, barrelInventory);
    }

    @Unique
    private List<ItemStack> collectFishingItemsFromInventory(VillagerEntity villager) {
        List<ItemStack> fishingItems = new ArrayList<>();

        for (int slotIndex = 0; slotIndex < villager.getInventory().size(); slotIndex++) {
            ItemStack currentStack = villager.getInventory().getStack(slotIndex);
            if (currentStack.isEmpty()) continue;

            if (isFishingRelatedItem(currentStack)) {
                fishingItems.add(currentStack.copy());
                villager.getInventory().removeStack(slotIndex);
            }
        }

        return fishingItems;
    }

    @Unique
    private boolean isFishingRelatedItem(ItemStack itemStack) {
        return itemStack.isOf(Items.COD) || itemStack.isOf(Items.SALMON) ||
               itemStack.isOf(Items.PUFFERFISH) || itemStack.isOf(Items.TROPICAL_FISH) ||
               itemStack.isOf(Items.STRING) || itemStack.isOf(Items.COAL) ||
               itemStack.isOf(Items.TRIPWIRE_HOOK);
    }

    @Unique
    private static int getItemStackLimit(final ItemStack item) {
        final var itemType = item.getItem();

        if (itemType == Items.COD || itemType == Items.SALMON ||
            itemType == Items.PUFFERFISH || itemType == Items.TROPICAL_FISH) {
            return 6;
        }

        if (itemType == Items.STRING || itemType == Items.TRIPWIRE_HOOK) {
            return 10;
        }

        if (itemType == Items.COAL) {
            return 4;
        }

        return 8;
    }

    @Unique
    private static int storeItemsInInventory(final List<ItemStack> items, final Inventory targetInventory) {
        final var randomizer = ThreadLocalRandom.current();
        var successfullyStored = 0;

        for (final var item : items) {
            final var itemLimit = getItemStackLimit(item);
            var stored = false;

            for (var slot = 0; slot < targetInventory.size() && !stored; slot++) {
                final var existingStack = targetInventory.getStack(slot);

                if (!existingStack.isEmpty() &&
                    existingStack.getItem() == item.getItem() &&
                    existingStack.getCount() < itemLimit) {

                    final var spaceAvailable = itemLimit - existingStack.getCount();
                    final var amountToAdd = Math.min(spaceAvailable, item.getCount());

                    existingStack.setCount(existingStack.getCount() + amountToAdd);
                    item.setCount(item.getCount() - amountToAdd);

                    if (item.getCount() == 0) {
                        stored = true;
                        successfullyStored++;
                    }
                }
            }

            if (!stored && item.getCount() > 0) {
                int maxAttempts = 10;

                while (maxAttempts-- > 0 && !stored) {
                    int randomSlot = randomizer.nextInt(targetInventory.size());
                    if (targetInventory.getStack(randomSlot).isEmpty()) {
                        int amountToStore = Math.min(item.getCount(), itemLimit);
                        ItemStack newStack = item.copy();
                        newStack.setCount(amountToStore);

                        targetInventory.setStack(randomSlot, newStack);
                        item.setCount(item.getCount() - amountToStore);

                        if (item.getCount() == 0) {
                            stored = true;
                            successfullyStored++;
                        }
                    }
                }
            }
        }

        return successfullyStored;
    }

    @Unique
    private void executeComprehensiveFishingStateResetAndCleanup(final VillagerEntity fisherVillagerEntity) {
        state.isCurrentlyFishing = false;
        state.currentFishingTarget = null;
        state.fishingLookTimer = 0;
        state.needsHandCleanup = false;
        state.handCleanupTimer = 0;
        state.hasItemsForBarrel = false;
        state.lastItemCheckDay = -1;
        state.targetWaterPosition = null;
        state.fishingResultPredetermined = false;
        state.willFishingSucceed = false;
        state.fishingEventTime = 0;
        state.animationDuration = 0;
        state.fishingEventTriggered = false;

        fisherVillagerEntity.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);

        executeVillagerBobberCleanupWithOptionalAudioFeedback(true);

        state.clearAllCaches();
    }

    @Override
    public void aiVillagersFabric$interruptTasks() {
        if (!state.isCurrentlyFishing) return;

        VillagerEntity villager = (VillagerEntity)(Object)this;

        if (state.currentFishingTarget != null) {
            releaseSpotGlobally(state.currentFishingTarget);
        }
        executeComprehensiveFishingStateResetAndCleanup(villager);
        state.fishingCooldownTimer = calculateExperienceBasedAdaptiveFishingCooldownDuration();
    }

    @Inject(method = "writeCustomData", at = @At("HEAD"))
    private void onWriteCustomData(WriteView dataView, CallbackInfo ci) {
        dataView.putInt("AiVillagers_FishingCooldownTimer", state.fishingCooldownTimer);
        dataView.putBoolean("AiVillagers_NeedsHandCleanup", state.needsHandCleanup);
        dataView.putInt("AiVillagers_HandCleanupTimer", state.handCleanupTimer);

        dataView.putBoolean("AiVillagers_IsCurrentlyFishing", state.isCurrentlyFishing);

        dataView.putInt("AiVillagers_ConsecutiveFailures", state.consecutiveFailures);
        dataView.putInt("AiVillagers_ConsecutiveSuccesses", state.consecutiveSuccesses);
        dataView.putLong("AiVillagers_LastFishingAttemptTime", state.lastFishingAttemptTime);

        dataView.putLong("AiVillagers_PersonalBarrelWorkTime", state.personalBarrelWorkTime);
        dataView.putLong("AiVillagers_LastWorkSessionTime", state.lastWorkSessionTime);
        dataView.putBoolean("AiVillagers_HasItemsForBarrel", state.hasItemsForBarrel);
        dataView.putLong("AiVillagers_LastItemCheckDay", state.lastItemCheckDay);

        if (state.lastSuccessfulFishingSpot != null) {
            dataView.putInt("AiVillagers_LastSuccessfulSpotX", state.lastSuccessfulFishingSpot.getX());
            dataView.putInt("AiVillagers_LastSuccessfulSpotY", state.lastSuccessfulFishingSpot.getY());
            dataView.putInt("AiVillagers_LastSuccessfulSpotZ", state.lastSuccessfulFishingSpot.getZ());
        }
    }

    @Inject(method = "readCustomData", at = @At("HEAD"))
    private void onReadCustomData(ReadView dataView, CallbackInfo ci) {
        state.fishingCooldownTimer = dataView.getInt("AiVillagers_FishingCooldownTimer", 0);
        state.needsHandCleanup = dataView.getBoolean("AiVillagers_NeedsHandCleanup", false);
        state.handCleanupTimer = dataView.getInt("AiVillagers_HandCleanupTimer", 0);

        boolean isCurrentlyFishing = dataView.getBoolean("AiVillagers_IsCurrentlyFishing", false);
        if (isCurrentlyFishing) {
            VillagerEntity villager = (VillagerEntity)(Object)this;
            executeComprehensiveFishingStateResetAndCleanup(villager);
            state.fishingCooldownTimer = 100;
            state.isCurrentlyFishing = false;
            state.currentFishingTarget = null;

            state.needsHandCleanup = true;
            state.handCleanupTimer = 40;
        }

        state.consecutiveFailures = dataView.getInt("AiVillagers_ConsecutiveFailures", 0);
        state.consecutiveSuccesses = dataView.getInt("AiVillagers_ConsecutiveSuccesses", 0);
        state.lastFishingAttemptTime = dataView.getLong("AiVillagers_LastFishingAttemptTime", 0);

        state.personalBarrelWorkTime = dataView.getLong("AiVillagers_PersonalBarrelWorkTime", -1);
        state.lastWorkSessionTime = dataView.getLong("AiVillagers_LastWorkSessionTime", -1);
        state.hasItemsForBarrel = dataView.getBoolean("AiVillagers_HasItemsForBarrel", false);
        state.lastItemCheckDay = dataView.getLong("AiVillagers_LastItemCheckDay", -1);

        Optional<Integer> lastSpotX = dataView.getOptionalInt("AiVillagers_LastSuccessfulSpotX");
        Optional<Integer> lastSpotY = dataView.getOptionalInt("AiVillagers_LastSuccessfulSpotY");
        Optional<Integer> lastSpotZ = dataView.getOptionalInt("AiVillagers_LastSuccessfulSpotZ");

        if (lastSpotX.isPresent() && lastSpotY.isPresent() && lastSpotZ.isPresent()) {
            state.lastSuccessfulFishingSpot = new BlockPos(lastSpotX.get(), lastSpotY.get(), lastSpotZ.get());
        }
    }

    @Unique
    private void cleanupFailedSpotsWithoutWater() {
        FisherWorldData data = retrieveGlobalFishingDataManager();
        if (data != null) {
            World world = this.getWorld();
            data.failedFishingSpots.entrySet().removeIf(entry -> {
                BlockPos pos = entry.getKey();
                BlockPos waterPos = pos.down();
                boolean hasWater = world.getBlockState(waterPos).getFluidState().isIn(FluidTags.WATER);
                boolean isSurfaceWater = world.getBlockState(waterPos.up()).isAir();

                if (!hasWater || !isSurfaceWater) {
                    LOGGER.info("Eliminando spot fallido {} - sin agua o agua subterránea", pos);
                    return true;
                }
                return false;
            });
        }
    }
}