package AiVillagers.mixins.professions.fisher;

import AiVillagers.entities.fishing.VillagerFishingBobberEntity;
import AiVillagers.interfaces.VillagerFishHookInterface;
import net.minecraft.entity.passive.VillagerEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;

@Mixin(VillagerEntity.class)
public class VillagerFishingBobberTrackingMixin implements VillagerFishHookInterface {
    @Unique
    private VillagerFishingBobberEntity aiVillagersFabric$fishHook;

    @Override
    public VillagerFishingBobberEntity aiVillagersFabric$getFishHook() {
        return this.aiVillagersFabric$fishHook;
    }

    @Override
    public void aiVillagersFabric$setFishHook(VillagerFishingBobberEntity fishHook) {
        this.aiVillagersFabric$fishHook = fishHook;
    }
}
