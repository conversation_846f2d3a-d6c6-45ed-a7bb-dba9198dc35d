package AiVillagers.mixins.professions.farmer;

import AiVillagers.data.GlobalWorldData;
import AiVillagers.interfaces.AnimalEntityInterface;
import net.minecraft.block.entity.BlockEntity;
import net.minecraft.block.entity.ChestBlockEntity;
import net.minecraft.entity.ai.brain.MemoryModuleType;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.entity.passive.ChickenEntity;
import net.minecraft.entity.passive.CowEntity;
import net.minecraft.entity.passive.PigEntity;
import net.minecraft.entity.passive.SheepEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.particle.ParticleTypes;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.sound.SoundCategory;
import net.minecraft.sound.SoundEvents;
import net.minecraft.storage.ReadView;
import net.minecraft.storage.WriteView;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.GlobalPos;
import net.minecraft.village.VillagerData;
import net.minecraft.village.VillagerProfession;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.*;

@Mixin(VillagerEntity.class)
public abstract class FarmerBehaviorMixin {

    // ===== CONSTANTES =====
    @Unique
    private static final double ANIMAL_SEARCH_RANGE = 6.0;
    @Unique
    private static final int CHEST_SEARCH_COOLDOWN = 2050;
    @Unique
    private static final int CHEST_INTERACTION_DELAY = 40;

    // ===== ESTADO DE ALIMENTACIÓN =====
    @Unique
    private final List<AnimalEntity> breedingPair = new ArrayList<>();
    @Unique
    private Item breedingFood = null;
    @Unique
    private int breedingStep = 0;
    @Unique
    private int breedingObservationTimer = 0;
    @Unique
    private int breedingDelay = 0;
    @Unique
    private int totalPairsFed = 0;
    @Unique
    private int globalBreedingCooldown = 0;
    @Unique
    private int lookingTimer = 0;
    @Unique
    private int freezeTimer = 0;
    @Unique
    private int pairRegenerationTimer = 0;

    // ===== ESTADO DE COFRES =====
    @Unique
    private int chestCooldownTimer = 0;
    @Unique
    private boolean isDepositingItems = false;
    @Unique
    private int chestInteractionTimer = 0;
    @Unique
    private ChestBlockEntity targetChest = null;
    @Unique
    private boolean chestOpened = false;
    @Unique
    private BlockPos personalChestPos = null;
    @Unique
    private int chestSearchTimer = 0;
    @Unique
    private int chestDelay = 0;
    @Unique
    private boolean hasDepositedToday = false;

    // ===== ESTADO DE COFRES RESERVADOS =====
    @Unique
    private GlobalWorldData worldData = null;

    // ===== UTILIDADES =====
    @Unique
    private final Random random = new Random();
    

    @Inject(method = "tick", at = @At("HEAD"))
    private void onFarmerTick(CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;
        World world = villager.getWorld();

        if (world.isClient() || villager.isSleeping()) {
            return;
        }

        if (!isFarmerProfession(villager)) {
            return;
        }

        handleSleepTimeReset(villager, world);

        if (isSleepTime(world)) {
            return;
        }

        initializeWorldData(world);
        processAnimalBreeding(villager, world);
        processChestDepositing(villager, world);
        handleAnimalAttraction(villager, world);
    }

    // ===== MÉTODOS DE VALIDACIÓN =====

    @Unique
    private boolean isFarmerProfession(VillagerEntity villager) {
        return villager.getVillagerData().profession().matchesKey(VillagerProfession.FARMER);
    }

    @Unique
    private boolean isSleepTime(World world) {
        long timeOfDay = world.getTimeOfDay() % 24000;
        return timeOfDay >= 9000;
    }

    @Unique
    private void handleSleepTimeReset(VillagerEntity villager, World world) {
        if (isSleepTime(world)) {
            long timeOfDay = world.getTimeOfDay() % 24000;

            // Reset inicial al comenzar el período de inactividad
            if (timeOfDay == 9000) {
                resetBreedingProcess();
                resetChestDepositing();

                // Resetear contadores de alimentación cada día
                totalPairsFed = 0;
                globalBreedingCooldown = 0;
                pairRegenerationTimer = 0;
                hasDepositedToday = false;
                chestDelay = 0;
                System.out.println("[Farmer] Nuevo día: contadores reseteados, puede depositar nuevamente");

                // Verificar si el aldeano sigue siendo farmer, si no, liberar cofre personal
                if (!isFarmerProfession(villager) && personalChestPos != null) {
                    releasePersonalChest();
                    System.out.println("[Farmer] Aldeano ya no es farmer durante reset nocturno, cofre personal liberado");
                }
            }

            // Limpiar mano principal entre ticks 9000-9100
            if (timeOfDay >= 9000 && timeOfDay <= 9100) {
                villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
            }
        }
    }

    // ===== INICIALIZACIÓN =====

    @Unique
    private void initializeWorldData(World world) {
        if (worldData == null && world instanceof ServerWorld serverWorld) {
            worldData = GlobalWorldData.get(serverWorld);
        }
    }

    // ===== LÓGICA DE ALIMENTACIÓN DE ANIMALES =====

    @Unique
    private void processAnimalBreeding(VillagerEntity villager, World world) {
        // Reducir cooldown global
        if (globalBreedingCooldown > 0) {
            globalBreedingCooldown--;
        }

        // Reducir delay individual
        if (breedingDelay > 0) {
            breedingDelay--;
            return;
        }

        // Sistema de regeneración de parejas
        handlePairRegeneration();

        // Verificar si ya alimentó 3 parejas (6 animales)
        if (totalPairsFed >= 3) {
            // Si el cooldown global terminó, resetear contador
            if (globalBreedingCooldown <= 0) {
                totalPairsFed = 0;
                System.out.println("[Farmer] Cooldown global terminado, puede alimentar nuevas parejas");
            } else {
                // Mostrar tiempo restante cada 600 ticks (30 segundos)
                if (globalBreedingCooldown % 600 == 0) {
                    int minutesLeft = globalBreedingCooldown / 1200;
                    int secondsLeft = (globalBreedingCooldown % 1200) / 20;
                    System.out.println("[Farmer] Esperando cooldown: " + minutesLeft + "m " + secondsLeft + "s restantes");
                }
                return;
            }
        }

        findBreedingPair(villager, world);
        executeBreedingProcess(villager, world);
    }

    @Unique
    private void handlePairRegeneration() {
        // Solo regenerar si no está en cooldown global y ha alimentado al menos 1 pareja
        if (globalBreedingCooldown <= 0 && totalPairsFed > 0) {
            if (pairRegenerationTimer > 0) {
                pairRegenerationTimer--;

                // Log cada 30 segundos
                if (pairRegenerationTimer % 600 == 0) {
                    int minutesLeft = pairRegenerationTimer / 1200;
                    int secondsLeft = (pairRegenerationTimer % 1200) / 20;
                    System.out.println("[Farmer] Regenerando pareja: " + minutesLeft + "m " + secondsLeft + "s restantes (parejas: " + totalPairsFed + "/3)");
                }
            } else {
                // Regenerar una pareja
                totalPairsFed--;
                pairRegenerationTimer = 2400; // 2400 ticks = 2 minutos
                System.out.println("[Farmer] ✓ Pareja regenerada! Ahora puede alimentar " + (3 - totalPairsFed) + " parejas más");
            }
        }
    }

    @Unique
    private void findBreedingPair(VillagerEntity villager, World world) {
        if (!breedingPair.isEmpty() || world.getTime() % 20 != 0) {
            return;
        }

        Box searchBox = villager.getBoundingBox().expand(ANIMAL_SEARCH_RANGE);
        List<AnimalEntity> breedingCandidates = world.getEntitiesByClass(
                AnimalEntity.class,
                searchBox,
                animal -> {
                    if (!animal.isAlive() || animal.isBaby() || !animal.canEat() || animal.getLoveTicks() > 0) {
                        return false;
                    }
                    // Solo alimentar animales que pertenecen a aldeas
                    return animal instanceof AnimalEntityInterface access && access.aiVillagersFabric$isVillageAnimal();
                }
        );

        if (breedingCandidates.size() < 2) {
            return;
        }

        // Agrupar por tipo de animal (ya filtrados por aldea y no bebés en breedingCandidates)
        List<CowEntity> cows = breedingCandidates.stream()
                .filter(CowEntity.class::isInstance)
                .map(CowEntity.class::cast)
                .toList();
        List<SheepEntity> sheep = breedingCandidates.stream()
                .filter(SheepEntity.class::isInstance)
                .map(SheepEntity.class::cast)
                .toList();
        List<PigEntity> pigs = breedingCandidates.stream()
                .filter(PigEntity.class::isInstance)
                .map(PigEntity.class::cast)
                .toList();
        List<ChickenEntity> chickens = breedingCandidates.stream()
                .filter(ChickenEntity.class::isInstance)
                .map(ChickenEntity.class::cast)
                .toList();

        // Intentar formar pareja con cada tipo
        if (!tryFormBreedingPair(cows, Items.WHEAT, villager) && !tryFormBreedingPair(sheep, Items.WHEAT, villager) && !tryFormBreedingPair(pigs, Items.CARROT, villager)) {
            tryFormBreedingPair(chickens, Items.WHEAT_SEEDS, villager);
        }
    }

    @Unique
    private <T extends AnimalEntity> boolean tryFormBreedingPair(List<T> animals, Item requiredFood, VillagerEntity villager) {
        if (animals.size() < 2 || !hasItemInInventory(villager, requiredFood, 2)) {
            return false;
        }

        // Crear una lista mutable para poder ordenarla
        List<T> sortableAnimals = new ArrayList<>(animals);
        sortableAnimals.sort(Comparator.comparingDouble(villager::squaredDistanceTo));

        breedingPair.clear();
        breedingPair.add(sortableAnimals.get(0));
        breedingPair.add(sortableAnimals.get(1));
        breedingFood = requiredFood;
        breedingStep = 0;
        breedingObservationTimer = 0;

        System.out.println("[Farmer] ✓ Pareja seleccionada para alimentar:");
        System.out.println("[Farmer]   - Animal 1: " + sortableAnimals.get(0).getName().getString() + " en " + sortableAnimals.get(0).getBlockPos());
        System.out.println("[Farmer]   - Animal 2: " + sortableAnimals.get(1).getName().getString() + " en " + sortableAnimals.get(1).getBlockPos());
        System.out.println("[Farmer]   - Comida: " + requiredFood.getName().getString());
        return true;
    }

    @Unique
    private void executeBreedingProcess(VillagerEntity villager, World world) {
        if (breedingPair.isEmpty() || breedingFood == null) {
            return;
        }

        if (!areAnimalsValidForBreeding()) {
            System.out.println("[Farmer] ⚠️ Animales no válidos para alimentación, reseteando proceso (paso: " + breedingStep + ")");
            resetBreedingProcess();
            return;
        }

        AnimalEntity firstAnimal = breedingPair.get(0);
        AnimalEntity secondAnimal = breedingPair.get(1);

        switch (breedingStep) {
            case 0 -> navigateToFirstAnimal(villager, firstAnimal);
            case 1 -> lookAtFirstAnimal(villager, firstAnimal);
            case 2 -> feedFirstAnimal(villager, world, firstAnimal);
            case 3 -> freezeAfterFirstFeeding(villager);
            case 4 -> navigateToSecondAnimal(villager, secondAnimal);
            case 5 -> lookAtSecondAnimal(villager, secondAnimal);
            case 6 -> feedSecondAnimal(villager, world, secondAnimal);
            case 7 -> freezeAfterSecondFeeding(villager);
            case 8 -> observeBreeding(villager, world);
        }
    }

    @Unique
    private boolean areAnimalsValidForBreeding() {
        // Durante el proceso de alimentación, solo verificar que estén vivos y no sean bebés
        // No verificar canEat() porque después de alimentar uno, puede cambiar temporalmente
        return breedingPair.stream().allMatch(animal ->
            animal != null && animal.isAlive() && !animal.isBaby()
        );
    }

    @Unique
    private void navigateToFirstAnimal(VillagerEntity villager, AnimalEntity animal) {
        double distance = villager.squaredDistanceTo(animal);
        if (distance > 4.0) {
            villager.getNavigation().startMovingTo(animal.getX(), animal.getY(), animal.getZ(), 0.5);
            System.out.println("[Farmer] Navegando al primer animal (distancia: " + String.format("%.1f", Math.sqrt(distance)) + ")");
        } else {
            villager.getNavigation().stop();
            breedingStep = 1;
            lookingTimer = 40; // 40 ticks para mirar
            equipFoodItem(villager, breedingFood);
            System.out.println("[Farmer] Llegó al primer animal, iniciando mirada de 40 ticks");
        }
    }

    @Unique
    private void lookAtFirstAnimal(VillagerEntity villager, AnimalEntity animal) {
        // Parar navegación mientras mira
        villager.getNavigation().stop();
        villager.getLookControl().lookAt(animal, 30.0F, 30.0F);
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);

        if (lookingTimer > 0) {
            lookingTimer--;
            System.out.println("[Farmer] Mirando primer animal... " + lookingTimer + " ticks restantes");
        } else {
            breedingStep = 2;
            System.out.println("[Farmer] Terminó de mirar primer animal, procediendo a alimentar");
        }
    }

    @Unique
    private void feedFirstAnimal(VillagerEntity villager, World world, AnimalEntity animal) {
        System.out.println("[Farmer] Intentando alimentar primer animal: " + animal.getName().getString());
        if (performFeedingAction(villager, world, animal)) {
            breedingStep = 3;
            freezeTimer = 60; // 60 ticks de freeze
            villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
            System.out.println("[Farmer] ✓ Primer animal alimentado exitosamente, iniciando freeze de 60 ticks");
        } else {
            System.out.println("[Farmer] ✗ Error al alimentar primer animal");
        }
    }

    @Unique
    private void freezeAfterFirstFeeding(VillagerEntity villager) {
        // Parar completamente la navegación durante el freeze
        villager.getNavigation().stop();

        if (freezeTimer > 0) {
            freezeTimer--;
            if (freezeTimer % 20 == 0) { // Log cada segundo
                System.out.println("[Farmer] Freeze después del primer animal... " + (freezeTimer / 20) + " segundos restantes");
            }
        } else {
            breedingStep = 4;
            System.out.println("[Farmer] Freeze terminado, yendo al segundo animal...");
        }
    }

    @Unique
    private void navigateToSecondAnimal(VillagerEntity villager, AnimalEntity animal) {
        double distance = villager.squaredDistanceTo(animal);
        if (distance > 4.0) {
            villager.getNavigation().startMovingTo(animal.getX(), animal.getY(), animal.getZ(), 0.5);
            System.out.println("[Farmer] Navegando al segundo animal (distancia: " + String.format("%.1f", Math.sqrt(distance)) + ")");
        } else {
            villager.getNavigation().stop();
            breedingStep = 5;
            lookingTimer = 40; // 40 ticks para mirar
            equipFoodItem(villager, breedingFood);
            System.out.println("[Farmer] Llegó al segundo animal, iniciando mirada de 40 ticks");
        }
    }

    @Unique
    private void lookAtSecondAnimal(VillagerEntity villager, AnimalEntity animal) {
        // Parar navegación mientras mira
        villager.getNavigation().stop();
        villager.getLookControl().lookAt(animal, 30.0F, 30.0F);
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);

        if (lookingTimer > 0) {
            lookingTimer--;
            System.out.println("[Farmer] Mirando segundo animal... " + lookingTimer + " ticks restantes");
        } else {
            breedingStep = 6;
            System.out.println("[Farmer] Terminó de mirar segundo animal, procediendo a alimentar");
        }
    }

    @Unique
    private void feedSecondAnimal(VillagerEntity villager, World world, AnimalEntity animal) {
        System.out.println("[Farmer] Intentando alimentar segundo animal: " + animal.getName().getString());
        if (performFeedingAction(villager, world, animal)) {
            breedingStep = 7;
            freezeTimer = 60; // 60 ticks de freeze
            villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
            System.out.println("[Farmer] ✓ Segundo animal alimentado exitosamente, iniciando freeze de 60 ticks");
        } else {
            System.out.println("[Farmer] ✗ Error al alimentar segundo animal");
        }
    }

    @Unique
    private void freezeAfterSecondFeeding(VillagerEntity villager) {
        // Parar completamente la navegación durante el freeze
        villager.getNavigation().stop();

        if (freezeTimer > 0) {
            freezeTimer--;
            if (freezeTimer % 20 == 0) { // Log cada segundo
                System.out.println("[Farmer] Freeze después del segundo animal... " + (freezeTimer / 20) + " segundos restantes");
            }
        } else {
            breedingStep = 8;
            breedingObservationTimer = 100;
            System.out.println("[Farmer] Ambos animales alimentados, observando reproducción...");
        }
    }

    @Unique
    private void observeBreeding(VillagerEntity villager, World world) {
        if (breedingObservationTimer > 0) {
            breedingObservationTimer--;

            if (!breedingPair.isEmpty()) {
                AnimalEntity targetAnimal = breedingPair.getFirst();
                villager.getLookControl().lookAt(targetAnimal, 30.0F, 30.0F);
            }
        } else {
            // Incrementar contador de parejas alimentadas
            totalPairsFed++;
            System.out.println("[Farmer] Pareja " + totalPairsFed + "/3 alimentada exitosamente");

            // Si completó 3 parejas, iniciar cooldown global
            if (totalPairsFed >= 3) {
                globalBreedingCooldown = 2400; // 2400 ticks = 2 minutos
                System.out.println("[Farmer] ¡3 parejas alimentadas! Iniciando cooldown de 2 minutos");
            } else {
                // Si no está en máximo, iniciar timer de regeneración
                if (pairRegenerationTimer <= 0) {
                    pairRegenerationTimer = 2400; // 2400 ticks = 2 minutos
                    System.out.println("[Farmer] Timer de regeneración iniciado: 2 minutos para recuperar una pareja");
                }
            }

            resetBreedingProcess();
            breedingDelay = 600 + random.nextInt(400);
        }
    }

    @Unique
    private boolean performFeedingAction(VillagerEntity villager, World world, AnimalEntity animal) {
        villager.getLookControl().lookAt(animal, 30.0F, 30.0F);
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);

        if (consumeItemFromInventory(villager, breedingFood)) {
            if (animal.canEat() && !animal.isInLove()) {
                animal.lovePlayer(null);

                if (world instanceof ServerWorld serverWorld) {
                    serverWorld.spawnParticles(ParticleTypes.HEART,
                            animal.getX(), animal.getY() + 0.5, animal.getZ(),
                            3, 0.2, 0.2, 0.2, 0.1);
                }

                System.out.println("[Farmer] Animal alimentado exitosamente");
                return true;
            }
        }
        return false;
    }

    @Unique
    private void equipFoodItem(VillagerEntity villager, Item food) {
        ItemStack currentStack = villager.getStackInHand(Hand.MAIN_HAND);
        if (currentStack.isEmpty() || currentStack.getItem() != food) {
            villager.setStackInHand(Hand.MAIN_HAND, new ItemStack(food));
        }
    }

    @Unique
    private void resetBreedingProcess() {
        System.out.println("[Farmer] 🔄 Reseteando proceso de alimentación (paso anterior: " + breedingStep + ")");
        breedingPair.clear();
        breedingFood = null;
        breedingStep = 0;
        breedingObservationTimer = 0;
        lookingTimer = 0;
        freezeTimer = 0;
    }

    // ===== MÉTODOS DE UTILIDAD =====

    @Unique
    private boolean hasItemInInventory(VillagerEntity villager, Item item, int minCount) {
        int count = 0;
        for (int i = 0; i < villager.getInventory().size(); i++) {
            ItemStack stack = villager.getInventory().getStack(i);
            if (stack.getItem() == item) {
                count += stack.getCount();
                if (count >= minCount) {
                    return true;
                }
            }
        }
        return false;
    }

    @Unique
    private boolean consumeItemFromInventory(VillagerEntity villager, Item item) {
        for (int i = 0; i < villager.getInventory().size(); i++) {
            ItemStack stack = villager.getInventory().getStack(i);
            if (stack.getItem() == item && !stack.isEmpty()) {
                stack.decrement(1);
                if (stack.isEmpty()) {
                    villager.getInventory().setStack(i, ItemStack.EMPTY);
                }
                return true;
            }
        }
        return false;
    }

    @Unique
    private Item getRequiredFoodItem(AnimalEntity animal) {
        if (animal instanceof CowEntity || animal instanceof SheepEntity) {
            return Items.WHEAT;
        } else if (animal instanceof PigEntity) {
            return Items.CARROT;
        } else if (animal instanceof ChickenEntity) {
            return Items.WHEAT_SEEDS;
        }
        return null;
    }

    // ===== LÓGICA DE COFRES =====

    @Unique
    private void processChestDepositing(VillagerEntity villager, World world) {
        // Buscar cofre personal cada 120 ticks (6 segundos) solo si tiene cama Y no tiene cofre personal
        if (personalChestPos == null) {
            chestSearchTimer++;
            if (chestSearchTimer >= 120 && villagerHasBed(villager)) {
                searchForPersonalChest(villager, world);
                chestSearchTimer = 0;
            }
        } else {
            // Si ya tiene cofre personal, verificar que sigue existiendo cada 600 ticks (30 segundos)
            chestSearchTimer++;
            if (chestSearchTimer >= 600) {
                verifyPersonalChestExists(villager);
                chestSearchTimer = 0;
            }
        }

        // Reducir delay de cofre
        if (chestDelay > 0) {
            chestDelay--;
        }

        // Solo procesar cofres entre ticks 8500-9000 y si no ha depositado hoy
        long timeOfDay = world.getTimeOfDay() % 24000;
        boolean isDepositTime = timeOfDay >= 8500 && timeOfDay <= 9000;

        if (isDepositTime && !hasDepositedToday && chestDelay <= 0) {
            if (!isDepositingItems) {
                handleChestSearchCooldown(villager);
            } else {
                handleChestInteraction(villager, world);
            }
        }
    }

    @Unique
    private boolean villagerHasBed(VillagerEntity villager) {
        Optional<GlobalPos> bedPosOptional = villager.getBrain().getOptionalMemory(MemoryModuleType.HOME);
        return Objects.requireNonNull(bedPosOptional).isPresent() &&
               bedPosOptional.get().dimension().equals(villager.getWorld().getRegistryKey());
    }

    @Unique
    private void searchForPersonalChest(VillagerEntity villager, World world) {
        if (!(world instanceof ServerWorld serverWorld)) {
            return;
        }

        ChestBlockEntity foundChest = findNearestChest(villager);
        if (foundChest != null) {
            personalChestPos = foundChest.getPos();

            // Mostrar partículas de happy_villager sobre el aldeano
            serverWorld.spawnParticles(ParticleTypes.HAPPY_VILLAGER,
                    villager.getX(), villager.getY() + 2.0, villager.getZ(),
                    3, 0.3, 0.3, 0.3, 0.1);

            System.out.println("[Farmer] ¡Cofre personal encontrado en: " + personalChestPos + "!");
        }
    }

    @Unique
    private void handleChestSearchCooldown(VillagerEntity villager) {
        chestCooldownTimer++;
        if (chestCooldownTimer >= CHEST_SEARCH_COOLDOWN) {
            ChestBlockEntity foundChest = null;

            // Si tiene cofre personal, usarlo directamente
            if (personalChestPos != null) {
                BlockEntity be = villager.getWorld().getBlockEntity(personalChestPos);
                if (be instanceof ChestBlockEntity chest) {
                    foundChest = chest;
                    System.out.println("[Farmer] Usando cofre personal en: " + personalChestPos);
                } else {
                    // El cofre personal ya no existe, buscar uno nuevo
                    personalChestPos = null;
                    System.out.println("[Farmer] Cofre personal perdido, buscando nuevo...");
                }
            }

            // Si no tiene cofre personal o se perdió, buscar uno nuevo
            if (foundChest == null) {
                foundChest = findNearestChest(villager);
            }

            if (foundChest != null) {
                isDepositingItems = true;
                targetChest = foundChest;
            }
            chestCooldownTimer = 0;
        }
    }

    @Unique
    private void handleChestInteraction(VillagerEntity villager, World world) {
        if (targetChest == null) {
            resetChestDepositing();
            return;
        }

        BlockPos chestPos = targetChest.getPos();
        double distanceSq = villager.squaredDistanceTo(chestPos.getX() + 0.5, chestPos.getY() + 0.5, chestPos.getZ() + 0.5);

        if (distanceSq > 4.0) {
            villager.getNavigation().startMovingTo(chestPos.getX() + 0.5, chestPos.getY(), chestPos.getZ() + 0.5, 0.5);
        } else {
            // Parar navegación cuando esté cerca del cofre para guardar cosas
            villager.getNavigation().stop();
            performChestInteraction(villager, world, chestPos);
        }
    }

    @Unique
    private void performChestInteraction(VillagerEntity villager, World world, BlockPos chestPos) {
        villager.getLookControl().lookAt(chestPos.getX() + 0.5, chestPos.getY() + 0.5, chestPos.getZ() + 0.5, 30.0F, 30.0F);
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);

        if (!chestOpened) {
            world.playSound(null, chestPos, SoundEvents.BLOCK_CHEST_OPEN, SoundCategory.BLOCKS, 1.0f, 1.0f);
            world.addSyncedBlockEvent(chestPos, targetChest.getCachedState().getBlock(), 1, 1);
            chestOpened = true;
            if (!villager.getInventory().isEmpty()) {
                depositItemsInChest(villager, targetChest);
            }
        }

        if (chestInteractionTimer <= 0) {
            chestInteractionTimer = CHEST_INTERACTION_DELAY;
        } else {
            chestInteractionTimer--;
            if (chestInteractionTimer <= 0) {
                world.addSyncedBlockEvent(chestPos, targetChest.getCachedState().getBlock(), 1, 0);
                world.playSound(null, chestPos, SoundEvents.BLOCK_CHEST_CLOSE, SoundCategory.BLOCKS, 1.0f, 1.0f);
                resetChestDepositing();
            }
        }
    }

    @Unique
    private ChestBlockEntity findNearestChest(VillagerEntity villager) {
        if (worldData == null) {
            return null;
        }

        // Limpiar cofres que ya no existen
        worldData.reservedChests.removeIf(pos -> villager.getWorld().getBlockState(pos).getBlock() != net.minecraft.block.Blocks.CHEST);
        if (!worldData.reservedChests.isEmpty()) {
            worldData.markDirty();
        }

        // Buscar desde la posición de la cama del aldeano
        BlockPos searchCenter = villager.getBlockPos(); // Por defecto desde el aldeano

        // Si tiene cama asignada, buscar desde ahí
        Optional<GlobalPos> bedPosOptional = villager.getBrain().getOptionalMemory(MemoryModuleType.HOME);
        if (Objects.requireNonNull(bedPosOptional).isPresent()) {
            GlobalPos bedGlobalPos = bedPosOptional.get();
            if (bedGlobalPos.dimension().equals(villager.getWorld().getRegistryKey())) {
                searchCenter = bedGlobalPos.pos();
                System.out.println("[Farmer] Buscando cofre desde la cama en: " + searchCenter);
            }
        }

        int radiusXZ = 22;
        int radiusY = 3;
        ChestBlockEntity nearestChest = null;
        double nearestDistance = Double.MAX_VALUE;

        for (int x = -radiusXZ; x <= radiusXZ; x++) {
            for (int y = -radiusY; y <= radiusY; y++) {
                for (int z = -radiusXZ; z <= radiusXZ; z++) {
                    BlockPos pos = searchCenter.add(x, y, z);
                    if (villager.getWorld().getChunkManager().isChunkLoaded(pos.getX() >> 4, pos.getZ() >> 4)) {
                        BlockEntity be = villager.getWorld().getBlockEntity(pos);
                        if (be instanceof ChestBlockEntity chest) {
                            // Verificar que el cofre no esté reservado por otro aldeano
                            if (!isChestReserved(pos, villager)) {
                                double distanceSq = searchCenter.getSquaredDistance(pos);
                                if (distanceSq < nearestDistance) {
                                    nearestDistance = distanceSq;
                                    nearestChest = chest;
                                }
                            }
                        }
                    }
                }
            }
        }

        // Si encontró un cofre, reservarlo (excepto si es su cofre personal)
        if (nearestChest != null) {
            BlockPos chestPos = nearestChest.getPos();

            // Solo reservar si no es el cofre personal
            if (personalChestPos == null || !personalChestPos.equals(chestPos)) {
                worldData.reservedChests.add(chestPos);
                worldData.markDirty();
                System.out.println("[Farmer] Cofre temporal reservado en: " + chestPos);
            } else {
                System.out.println("[Farmer] Usando cofre personal (ya reservado): " + chestPos);
            }
        }

        return nearestChest;
    }

    @Unique
    private boolean isChestReserved(BlockPos chestPos, VillagerEntity currentVillager) {
        // Verificar si está en la lista de cofres reservados
        if (worldData.reservedChests.contains(chestPos)) {
            System.out.println("[Farmer] Cofre en " + chestPos + " está reservado por otro aldeano");
            return true;
        }

        // Verificar si algún aldeano cercano lo está usando actualmente
        currentVillager.getWorld().getEntitiesByClass(
                VillagerEntity.class,
                new Box(chestPos).expand(50),
                v -> !v.equals(currentVillager) && v.isAlive()
        );

        return false;
    }

    @Unique
    private void depositItemsInChest(VillagerEntity villager, ChestBlockEntity chest) {
        for (int i = 0; i < villager.getInventory().size(); i++) {
            ItemStack stack = villager.getInventory().getStack(i);
            if (!stack.isEmpty()) {
                depositStackIntoChest(chest, stack.copy());
                villager.getInventory().setStack(i, ItemStack.EMPTY);
            }
        }
    }

    @Unique
    private void depositStackIntoChest(ChestBlockEntity chest, ItemStack stack) {
        int size = chest.size();
        int[] emptySlots = new int[size];
        int emptyCount = 0;
        for (int i = 0; i < size; i++) {
            if (chest.getStack(i).isEmpty()) {
                emptySlots[emptyCount++] = i;
            }
        }
        if (emptyCount > 0) {
            int randomSlot = emptySlots[random.nextInt(emptyCount)];
            chest.setStack(randomSlot, stack);
            return;
        }
        for (int i = 0; i < size; i++) {
            ItemStack chestStack = chest.getStack(i);
            if (!chestStack.isEmpty() && chestStack.getItem() == stack.getItem() && ItemStack.areItemsEqual(chestStack, stack)) {
                int max = chestStack.getMaxCount();
                int transferable = Math.min(stack.getCount(), max - chestStack.getCount());
                chestStack.increment(transferable);
                stack.decrement(transferable);
                if (stack.isEmpty()) {
                    chest.setStack(i, chestStack);
                    return;
                }
            }
        }
    }

    @Unique
    private void resetChestDepositing() {
        // Solo liberar el cofre del sistema de reservas si NO es el cofre personal
        if (targetChest != null && worldData != null) {
            BlockPos targetPos = targetChest.getPos();

            // Si no es el cofre personal, liberarlo del sistema de reservas
            if (personalChestPos == null || !personalChestPos.equals(targetPos)) {
                worldData.reservedChests.remove(targetPos);
                worldData.markDirty();
                System.out.println("[Farmer] Cofre temporal liberado: " + targetPos);
            } else {
                System.out.println("[Farmer] Terminó de usar cofre personal: " + targetPos);
            }
        }

        // Marcar que ya depositó hoy y establecer delay de 1200 ticks
        hasDepositedToday = true;
        chestDelay = 1200;

        isDepositingItems = false;
        chestOpened = false;
        targetChest = null;
        chestCooldownTimer = 0;
        chestInteractionTimer = 0;

        System.out.println("[Farmer] Depósito completado, delay de 1200 ticks establecido");
    }

    // ===== ATRACCIÓN DE ANIMALES =====

    @Unique
    private void handleAnimalAttraction(VillagerEntity villager, World world) {
        ItemStack mainHandStack = villager.getStackInHand(Hand.MAIN_HAND);
        Box attractionBox = villager.getBoundingBox().expand(10.0);

        if (mainHandStack.isEmpty()) {
            stopAllAnimalAttraction(world, attractionBox);
            return;
        }

        Item heldItem = mainHandStack.getItem();
        if (heldItem == Items.CARROT) {
            List<PigEntity> pigs = world.getEntitiesByClass(PigEntity.class, attractionBox,
                pig -> pig.isAlive() && pig instanceof AnimalEntityInterface access && access.aiVillagersFabric$isVillageAnimal());
            attractAnimalsOfType(pigs, villager);
        } else if (heldItem == Items.WHEAT) {
            List<AnimalEntity> wheatAnimals = world.getEntitiesByClass(AnimalEntity.class, attractionBox,
                    entity -> (entity instanceof CowEntity || entity instanceof SheepEntity) && entity.isAlive()
                            && entity instanceof AnimalEntityInterface access && access.aiVillagersFabric$isVillageAnimal());
            attractAnimalsOfType(wheatAnimals, villager);
        } else if (heldItem == Items.WHEAT_SEEDS) {
            List<ChickenEntity> chickens = world.getEntitiesByClass(ChickenEntity.class, attractionBox,
                chicken -> chicken.isAlive() && chicken instanceof AnimalEntityInterface access && access.aiVillagersFabric$isVillageAnimal());
            attractAnimalsOfType(chickens, villager);
        }
    }

    @Unique
    private <T extends AnimalEntity> void attractAnimalsOfType(List<T> animals, VillagerEntity villager) {
        for (T animal : animals) {
            double speed = animal instanceof SheepEntity || animal instanceof CowEntity ? 1.2 : 0.9;

            if (animal.squaredDistanceTo(villager) > 4.0) {
                animal.getNavigation().startMovingTo(villager, speed);
                animal.getLookControl().lookAt(villager.getX(), villager.getBodyY(0.5D), villager.getZ());
                animal.getBrain().forget(MemoryModuleType.LOOK_TARGET);
            } else {
                animal.getNavigation().stop();
            }
        }
    }

    @Unique
    private void stopAllAnimalAttraction(World world, Box searchBox) {
        List<AnimalEntity> allAnimals = world.getEntitiesByClass(AnimalEntity.class, searchBox,
            animal -> animal.isAlive() && animal instanceof AnimalEntityInterface access && access.aiVillagersFabric$isVillageAnimal());
        for (AnimalEntity animal : allAnimals) {
            animal.getNavigation().stop();
        }
    }

    @Inject(method = "setVillagerData", at = @At("HEAD"))
    private void onSetVillagerData(VillagerData villagerData, CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;

        RegistryEntry<VillagerProfession> currentProfession = villager.getVillagerData().profession();

        RegistryEntry<VillagerProfession> newProfession = villagerData.profession();

        if (currentProfession != newProfession) {
            if (newProfession.matchesKey(VillagerProfession.FARMER)) {
                villager.getInventory().addStack(new ItemStack(Items.WHEAT_SEEDS, 3));
                villager.getInventory().addStack(new ItemStack(Items.BEETROOT_SEEDS, 2));
                villager.getInventory().addStack(new ItemStack(Items.CARROT, 3));
                villager.getInventory().addStack(new ItemStack(Items.POTATO, 3));
                villager.getInventory().addStack(new ItemStack(Items.BONE_MEAL, 2));
            } else if (currentProfession.matchesKey(VillagerProfession.FARMER)) {
                villager.getInventory().clear();
                villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
            }
        }
    }

    // ===== MANEJO DE PÉRDIDA DE COFRE PERSONAL =====

    @Inject(method = "onDeath", at = @At("HEAD"))
    private void onVillagerDeath(CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;
        if (isFarmerProfession(villager)) {
            releasePersonalChest();
            System.out.println("[Farmer] Aldeano murió, cofre personal liberado");
        }
    }

    @Inject(method = "setVillagerData", at = @At("HEAD"))
    private void onVillagerDataChange(VillagerData villagerData, CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;

        // Si tenía cofre personal y ya no es farmer, liberarlo
        if (personalChestPos != null && !villagerData.profession().matchesKey(VillagerProfession.FARMER)) {
            releasePersonalChest();
            System.out.println("[Farmer] Aldeano perdió trabajo de farmer, cofre personal liberado");
        }
    }

    @Unique
    private void verifyPersonalChestExists(VillagerEntity villager) {
        if (personalChestPos != null) {
            BlockEntity be = villager.getWorld().getBlockEntity(personalChestPos);
            if (!(be instanceof ChestBlockEntity)) {
                System.out.println("[Farmer] Cofre personal destruido, liberando posición: " + personalChestPos);
                releasePersonalChest();
            }
        }
    }

    @Unique
    private void releasePersonalChest() {
        if (personalChestPos != null && worldData != null) {
            // Liberar el cofre del sistema de reservas
            worldData.reservedChests.remove(personalChestPos);
            worldData.markDirty();

            System.out.println("[Farmer] Cofre personal liberado del sistema: " + personalChestPos);
            personalChestPos = null;
            chestSearchTimer = 0;
        }
    }

    // ===== PERSISTENCIA DE DATOS =====

    @Inject(method = "writeCustomData", at = @At("TAIL"))
    private void writeFarmerCustomData(WriteView view, CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;

        if (!isFarmerProfession(villager)) {
            return;
        }

        // Estado de alimentación
        view.putInt("farmer_breeding_step", breedingStep);
        view.putInt("farmer_breeding_observation_timer", breedingObservationTimer);
        view.putInt("farmer_breeding_delay", breedingDelay);
        view.putInt("farmer_total_pairs_fed", totalPairsFed);
        view.putInt("farmer_global_breeding_cooldown", globalBreedingCooldown);
        view.putInt("farmer_looking_timer", lookingTimer);
        view.putInt("farmer_freeze_timer", freezeTimer);
        view.putInt("farmer_pair_regeneration_timer", pairRegenerationTimer);
        if (breedingFood != null) {
            view.putString("farmer_breeding_food", breedingFood.toString());
        }

        // Estado de cofres
        view.putInt("farmer_chest_cooldown_timer", chestCooldownTimer);
        view.putBoolean("farmer_is_depositing_items", isDepositingItems);
        view.putInt("farmer_chest_interaction_timer", chestInteractionTimer);
        view.putBoolean("farmer_chest_opened", chestOpened);
        view.putInt("farmer_chest_search_timer", chestSearchTimer);
        view.putInt("farmer_chest_delay", chestDelay);
        view.putBoolean("farmer_has_deposited_today", hasDepositedToday);

        // Cofre personal
        if (personalChestPos != null) {
            view.putInt("farmer_personal_chest_x", personalChestPos.getX());
            view.putInt("farmer_personal_chest_y", personalChestPos.getY());
            view.putInt("farmer_personal_chest_z", personalChestPos.getZ());
        }
    }

    @Inject(method = "readCustomData", at = @At("TAIL"))
    private void readFarmerCustomData(ReadView view, CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;

        if (!isFarmerProfession(villager)) {
            return;
        }

        // Estado de alimentación
        breedingStep = view.getInt("farmer_breeding_step", 0);
        breedingObservationTimer = view.getInt("farmer_breeding_observation_timer", 0);
        breedingDelay = view.getInt("farmer_breeding_delay", 0);
        totalPairsFed = view.getInt("farmer_total_pairs_fed", 0);
        globalBreedingCooldown = view.getInt("farmer_global_breeding_cooldown", 0);
        lookingTimer = view.getInt("farmer_looking_timer", 0);
        freezeTimer = view.getInt("farmer_freeze_timer", 0);
        pairRegenerationTimer = view.getInt("farmer_pair_regeneration_timer", 0);

        view.getOptionalString("farmer_breeding_food").ifPresent(foodName -> {
            if (foodName.contains("wheat")) breedingFood = Items.WHEAT;
            else if (foodName.contains("carrot")) breedingFood = Items.CARROT;
            else if (foodName.contains("wheat_seeds")) breedingFood = Items.WHEAT_SEEDS;
        });

        // Estado de cofres
        chestCooldownTimer = view.getInt("farmer_chest_cooldown_timer", 0);
        isDepositingItems = view.getBoolean("farmer_is_depositing_items", false);
        chestInteractionTimer = view.getInt("farmer_chest_interaction_timer", 0);
        chestOpened = view.getBoolean("farmer_chest_opened", false);
        chestSearchTimer = view.getInt("farmer_chest_search_timer", 0);
        chestDelay = view.getInt("farmer_chest_delay", 0);
        hasDepositedToday = view.getBoolean("farmer_has_deposited_today", false);

        // Cofre personal
        view.getOptionalInt("farmer_personal_chest_x").ifPresent(x -> {
            int y = view.getInt("farmer_personal_chest_y", 0);
            int z = view.getInt("farmer_personal_chest_z", 0);
            personalChestPos = new BlockPos(x, y, z);
        });
    }
}
