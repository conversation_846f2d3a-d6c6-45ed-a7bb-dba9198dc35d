package AiVillagers.mixins.professions.nitwit;

import net.minecraft.block.Blocks;
import net.minecraft.block.TallPlantBlock;
import net.minecraft.block.enums.DoubleBlockHalf;
import net.minecraft.entity.ItemEntity;
import net.minecraft.entity.ai.brain.MemoryModuleType;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.inventory.SimpleInventory;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.random.Random;
import net.minecraft.village.VillagerProfession;
import net.minecraft.world.World;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.particle.ParticleTypes;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(VillagerEntity.class)
public abstract class NitwitBehaviorMixin {

    @Unique private static final int ADD_ITEM_TO_INVENTORY_INTERVAL = 2050;
    @Unique private static final int BONEMEAL_INTERVAL = 2950;
    @Unique private static final int FERTILIZATION_DELAY_TICKS = 80;
    @Unique private static final int SEARCH_RADIUS = 16;
    @Unique private static final int GRASS_SEARCH_ATTEMPTS = 5;
    @Unique private static final double WALKING_SPEED = 0.5;
    @Unique private static final double GRASS_REACH_DISTANCE = 1.5;

    @Unique private static final ItemStack[] GIFTS = new ItemStack[]{
            new ItemStack(Items.EMERALD),
            new ItemStack(Items.APPLE),
            new ItemStack(Items.TALL_GRASS),
            new ItemStack(Items.SHORT_GRASS),
            new ItemStack(Items.DANDELION),
            new ItemStack(Items.POPPY)
    };

    @Unique private int tickCounter = 0;
    @Unique private int fertilizeWaitTicks = -1;
    @Unique private int bonemealCooldown = 0;
    @Unique private BlockPos grassTargetBlock = null;
    @Unique private boolean walkingToGrass = false;
    @Unique private int lastEmptySlot = 0;

    @Inject(method = "tick", at = @At("HEAD"))
    private void tick(CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;
        World world = villager.getWorld();
        if (!isValidNitwit(villager, world)) return;

        tickCounter++;

        handleFertilizationCountdown(villager, world);

        handleWalkingToGrass(villager);

        handleGiftGiving(villager);

        handleBonemealAttempt(villager);

    }

    @Unique
    private void handleFertilizationCountdown(VillagerEntity villager, World world) {
        if (fertilizeWaitTicks <= 0) return;
        fertilizeWaitTicks--;
        if (grassTargetBlock != null) lookAtPosition(villager, grassTargetBlock);
        villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);
        villager.setStackInHand(Hand.MAIN_HAND, new ItemStack(Items.BONE_MEAL));
        if (fertilizeWaitTicks == 0 && world instanceof ServerWorld sw) {
            performFertilization(sw, villager, grassTargetBlock);
            villager.setStackInHand(Hand.MAIN_HAND, ItemStack.EMPTY);
            resetGrassTarget();
        }
    }

    @Unique
    private void handleWalkingToGrass(VillagerEntity villager) {
        if (walkingToGrass && grassTargetBlock != null) {
            lookAtPosition(villager, grassTargetBlock);
            villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);
            if (villager.getBlockPos().isWithinDistance(grassTargetBlock, GRASS_REACH_DISTANCE)
                    || villager.getNavigation().isIdle()) {
                villager.getNavigation().stop();
                walkingToGrass = false;
                fertilizeWaitTicks = FERTILIZATION_DELAY_TICKS;
            }
        }
    }

    @Unique
    private void handleGiftGiving(VillagerEntity villager) {
        if (bonemealCooldown > 0) bonemealCooldown--;
        if (tickCounter % ADD_ITEM_TO_INVENTORY_INTERVAL == 0) {
            giveRandomGift(villager);
        }
    }

    @Unique
    private void handleBonemealAttempt(VillagerEntity villager) {
        if (grassTargetBlock != null && walkingToGrass) {
            lookAtPosition(villager, grassTargetBlock);
            villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);
            villager.getNavigation().startMovingTo(
                    grassTargetBlock.getX(), grassTargetBlock.getY(), grassTargetBlock.getZ(), WALKING_SPEED
            );
            return;
        }
        if (bonemealCooldown <= 0 && grassTargetBlock == null) {
            findAndNavigateToGrass(villager);
            bonemealCooldown = BONEMEAL_INTERVAL;
        }
    }

    @Unique
    private void findAndNavigateToGrass(VillagerEntity villager) {
        if (!(villager.getWorld() instanceof ServerWorld sw)) return;
        Random rand = sw.getRandom();
        for (int i = 0; i < GRASS_SEARCH_ATTEMPTS; i++) {
            int dx = rand.nextInt(SEARCH_RADIUS * 2 + 1) - SEARCH_RADIUS;
            int dy = rand.nextInt(SEARCH_RADIUS * 2 + 1) - SEARCH_RADIUS;
            int dz = rand.nextInt(SEARCH_RADIUS * 2 + 1) - SEARCH_RADIUS;
            BlockPos pos = villager.getBlockPos().add(dx, dy, dz);
            if (sw.getBlockState(pos).isOf(Blocks.GRASS_BLOCK)) {
                grassTargetBlock = pos;
                walkingToGrass = true;
                villager.getNavigation().startMovingTo(pos.getX(), pos.getY(), pos.getZ(), WALKING_SPEED);
                lookAtPosition(villager, pos);
                villager.getBrain().forget(MemoryModuleType.LOOK_TARGET);
                return;
            }
        }
    }

    @Unique
    private boolean isValidNitwit(VillagerEntity villager, World world) {
        if (world == null || world.isClient) return false;
        var prof = villager.getVillagerData().profession();
        long time = world.getTimeOfDay();
        return prof.matchesKey(VillagerProfession.NITWIT) && (time < 9000 || time > 23000) && !villager.isSleeping();
    }

    @Unique
    private void giveRandomGift(VillagerEntity villager) {
        SimpleInventory inv = villager.getInventory();
        World world = villager.getWorld();
        ItemStack gift = GIFTS[world.random.nextInt(GIFTS.length)];
        int size = inv.size();
        for (int offset = 0; offset < size; offset++) {
            int i = (lastEmptySlot + offset) % size;
            if (inv.getStack(i).isEmpty()) {
                inv.setStack(i, gift);
                lastEmptySlot = i;
                return;
            }
        }
        if (world instanceof ServerWorld sw) {
            sw.spawnEntity(new ItemEntity(sw, villager.getX(), villager.getY(), villager.getZ(), gift));
        }
    }

    @Unique
    private void lookAtPosition(VillagerEntity villager, BlockPos pos) {
        villager.getLookControl().lookAt(pos.getX() + 0.5, pos.getY() + 0.5, pos.getZ() + 0.5);
    }

    @Unique
    private void performFertilization(ServerWorld world, VillagerEntity villager, BlockPos center) {
        generateFloraAround(world, villager.getBlockPos());
        world.spawnParticles(ParticleTypes.HAPPY_VILLAGER,
                center.getX() + 0.5, center.getY() + 1, center.getZ() + 0.5,
                30, 2, 0.5, 2, 0.1
        );
    }

    @Unique
    private void generateFloraAround(ServerWorld world, BlockPos center) {
        Random rand = world.getRandom();
        BlockPos.Mutable m = new BlockPos.Mutable();
        int radius = 2;
        BlockPos base = center.down();
        for (int dx = -radius; dx <= radius; dx++) {
            for (int dz = -radius; dz <= radius; dz++) {
                m.set(base.getX() + dx, base.getY(), base.getZ() + dz);
                if (world.getBlockState(m).isOf(Blocks.GRASS_BLOCK) && world.isAir(m.up())) {
                    float c = rand.nextFloat();
                    BlockPos top = m.up();
                    // Ajuste de probabilidades: menos flores y más hierba
                    if (c < 0.05f) {
                        world.setBlockState(top, Blocks.POPPY.getDefaultState());
                    } else if (c < 0.75f) {
                        world.setBlockState(top, Blocks.SHORT_GRASS.getDefaultState());
                    } else if (c < 0.80f) {
                        world.setBlockState(top, Blocks.DANDELION.getDefaultState());
                    } else if (world.isAir(top.up())) {
                        world.setBlockState(top,
                                Blocks.TALL_GRASS.getDefaultState().with(TallPlantBlock.HALF, DoubleBlockHalf.LOWER)
                        );
                        world.setBlockState(top.up(),
                                Blocks.TALL_GRASS.getDefaultState().with(TallPlantBlock.HALF, DoubleBlockHalf.UPPER)
                        );
                    }
                }
            }
        }
    }

    @Unique
    private void resetGrassTarget() {
        grassTargetBlock = null;
        walkingToGrass = false;
    }
}