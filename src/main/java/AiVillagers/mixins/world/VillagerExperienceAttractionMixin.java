package AiVillagers.mixins.world;

import AiVillagers.mixins.accessors.GlobalAccessor;
import net.minecraft.entity.ExperienceOrbEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.sound.SoundEvents;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;
import net.minecraft.village.VillagerProfession;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.Comparator;
import java.util.List;

@Mixin(ExperienceOrbEntity.class)
public abstract class VillagerExperienceAttractionMixin {

    @Unique
    private VillagerEntity aiVillagers$targetVillager;

    @Unique private static final double ATTRACT_RADIUS = 8.0;
    @Unique private static final double ATTRACT_RADIUS_SQ = ATTRACT_RADIUS * ATTRACT_RADIUS;
    @Unique private static final double COLLECT_BOX_EXPAND = 0.5;

    @Inject(method = "tick", at = @At("TAIL"))
    private void onTickEnd(CallbackInfo ci) {
        ExperienceOrbEntity orb = (ExperienceOrbEntity)(Object)this;
        World world = orb.getWorld();
        if (world.isClient) return;

        if (this.aiVillagers$targetVillager != null && (
                !this.aiVillagers$targetVillager.isAlive() ||
                        this.isInvalidProfession(this.aiVillagers$targetVillager.getVillagerData().profession()) ||
                        this.aiVillagers$targetVillager.squaredDistanceTo(orb) > ATTRACT_RADIUS_SQ
        )) {
            this.aiVillagers$targetVillager = null;
        }

        if (this.aiVillagers$targetVillager == null) {
            world.getEntitiesByClass(
                            VillagerEntity.class,
                            Box.of(orb.getPos(), ATTRACT_RADIUS, ATTRACT_RADIUS, ATTRACT_RADIUS),
                            v -> v.isAlive() && !this.isInvalidProfession(v.getVillagerData().profession())
                    ).stream()
                    .min(Comparator.comparingDouble(v -> v.squaredDistanceTo(orb)))
                    .ifPresent(closestVillager -> this.aiVillagers$targetVillager = closestVillager);
        }

        if (this.aiVillagers$targetVillager != null) {
            Vec3d toVillager = new Vec3d(
                    this.aiVillagers$targetVillager.getX() - orb.getX(),
                    this.aiVillagers$targetVillager.getY() + this.aiVillagers$targetVillager.getStandingEyeHeight() / 2.0 - orb.getY(),
                    this.aiVillagers$targetVillager.getZ() - orb.getZ()
            );

            double distSq = toVillager.lengthSquared();
            if (distSq < ATTRACT_RADIUS_SQ) {
                double distance = Math.sqrt(distSq);

                double minDistance = 1.5;
                double maxDistance = 3.5;
                double minCoeff = 0.08;
                double maxCoeff = 0.16;

                double clamped = Math.max(0.0, Math.min(1.0, (distance - minDistance) / (maxDistance - minDistance)));
                double coeff = minCoeff + (maxCoeff - minCoeff) * clamped;

                double e = 1.0 - (distance / ATTRACT_RADIUS);
                Vec3d delta = toVillager.normalize().multiply(e * e * coeff);
                orb.setVelocity(orb.getVelocity().add(delta));
            }
        }

        List<VillagerEntity> collectors = world.getEntitiesByClass(
                VillagerEntity.class,
                orb.getBoundingBox().expand(COLLECT_BOX_EXPAND),
                v -> v.isAlive() && !this.isInvalidProfession(v.getVillagerData().profession())
        );

        for (VillagerEntity villager : collectors) {
            if (tryCollectByVillager(orb, villager)) {
                break;
            }
        }
    }

    @Unique
    private boolean tryCollectByVillager(ExperienceOrbEntity orb, VillagerEntity villager) {
        if (orb.getBoundingBox().intersects(villager.getBoundingBox())) {
            World world = orb.getWorld();
            int xp = orb.getValue();
            GlobalAccessor acc = (GlobalAccessor)villager;

            acc.setExperience(acc.getExperience() + xp);
            if (acc.invokeCanLevelUp()) {
                acc.invokeLevelUp();
            }

            villager.playSound(SoundEvents.ENTITY_EXPERIENCE_ORB_PICKUP, 0.1F, (world.random.nextFloat() - world.random.nextFloat()) * 0.35F + 0.9F);
            orb.discard();
            return true;
        }
        return false;
    }

    @Unique
    private boolean isInvalidProfession(RegistryEntry<VillagerProfession> prof) {
        return prof.matchesKey(VillagerProfession.NONE) || prof.matchesKey(VillagerProfession.NITWIT);
    }
}