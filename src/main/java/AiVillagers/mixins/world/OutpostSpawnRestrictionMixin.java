package AiVillagers.mixins.world;

import AiVillagers.world.OutpostNearVillageGenerator;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.SpawnGroup;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.BlockPos.Mutable;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.world.SpawnHelper;
import net.minecraft.world.biome.SpawnSettings;
import net.minecraft.world.gen.StructureAccessor;
import net.minecraft.world.gen.chunk.ChunkGenerator;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(SpawnHelper.class)
public class OutpostSpawnRestrictionMixin {

    @Inject(method = "canSpawn", at = @At("HEAD"), cancellable = true)
    private static void cancelSpawnIfNotPillager(ServerWorld world, SpawnGroup group, StructureAccessor structureAccessor, ChunkGenerator chunkGenerator, SpawnSettings.SpawnEntry spawnEntry, Mutable pos, double squaredDistance, CallbackInfoReturnable<Boolean> cir) {
        if (OutpostNearVillageGenerator.isOutpostChunk(new ChunkPos(pos)) && spawnEntry.type() != EntityType.PILLAGER) {
            cir.setReturnValue(false);
        }
    }
}