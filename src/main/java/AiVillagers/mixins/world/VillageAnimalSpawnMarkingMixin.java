package AiVillagers.mixins.world;

import AiVillagers.interfaces.AnimalEntityInterface;
import net.minecraft.entity.EntityData;
import net.minecraft.entity.SpawnReason;
import net.minecraft.entity.passive.CowEntity;
import net.minecraft.entity.passive.PigEntity;
import net.minecraft.entity.passive.SheepEntity;
import net.minecraft.entity.mob.MobEntity;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.world.LocalDifficulty;
import net.minecraft.world.ServerWorldAccess;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(MobEntity.class)
public abstract class VillageAnimalSpawnMarkingMixin {

    @Inject(method = "initialize(Lnet/minecraft/world/ServerWorldAccess;Lnet/minecraft/world/LocalDifficulty;Lnet/minecraft/entity/SpawnReason;Lnet/minecraft/entity/EntityData;)Lnet/minecraft/entity/EntityData;", at = @At("TAIL"))
    private void onInitializeMarkVillageAnimal(ServerWorldAccess world, LocalDifficulty difficulty, SpawnReason spawnReason, EntityData entityData, CallbackInfoReturnable<EntityData> cir) {
        if ((Object) this instanceof AnimalEntity animal) {
            if (animal instanceof CowEntity || animal instanceof PigEntity || animal instanceof SheepEntity) {

                boolean structure = spawnReason == SpawnReason.STRUCTURE;
                boolean named = animal.hasCustomName();

                if (structure || named) {
                    AnimalEntityInterface access = (AnimalEntityInterface) animal;
                    access.aiVillagersFabric$setVillageAnimal(true);
                }
            }
        }
    }
}