package AiVillagers.mixins.world.animals;

import AiVillagers.interfaces.AnimalEntityInterface;
import net.minecraft.entity.passive.SheepEntity;
import net.minecraft.entity.passive.PassiveEntity;
import net.minecraft.server.world.ServerWorld;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(SheepEntity.class)
public abstract class SheepVillageStatusInheritanceMixin {

    @Inject(method = "createChild(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/passive/PassiveEntity;)Lnet/minecraft/entity/passive/SheepEntity;", at = @At("RETURN"))
    private void onCreateChild(ServerWorld world, PassiveEntity mate, CallbackInfoReturnable<SheepEntity> cir) {
        SheepEntity child = cir.getReturnValue();

        if (child instanceof AnimalEntityInterface accessChild
                && this instanceof AnimalEntityInterface accessThis
                && mate instanceof AnimalEntityInterface accessMate) {

            boolean thisVillage = accessThis.aiVillagersFabric$isVillageAnimal();
            boolean mateVillage = accessMate.aiVillagersFabric$isVillageAnimal();

            if (thisVillage && mateVillage) {
                accessChild.aiVillagersFabric$setVillageAnimal(true);
            } else if (!thisVillage && !mateVillage) {
                accessChild.aiVillagersFabric$setVillageAnimal(true);
                accessThis.aiVillagersFabric$setVillageAnimal(true);
                accessMate.aiVillagersFabric$setVillageAnimal(true);
            } else if (thisVillage) {
                accessChild.aiVillagersFabric$setVillageAnimal(true);
                accessMate.aiVillagersFabric$setVillageAnimal(true);
            } else {
                accessChild.aiVillagersFabric$setVillageAnimal(true);
                accessThis.aiVillagersFabric$setVillageAnimal(true);
            }
        }
    }
}