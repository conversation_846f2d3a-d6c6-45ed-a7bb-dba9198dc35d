package AiVillagers.mixins.world;

import net.minecraft.world.poi.PointOfInterestStorage;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.ModifyArg;
import org.spongepowered.asm.mixin.injection.At;

@Mixin(PointOfInterestStorage.class)
public class PointOfInterestSearchRadiusMixin {

    @ModifyArg(
            method = "getSortedTypesAndPositions",
            at = @At(
                    value = "INVOKE",
                    target = "Lnet/minecraft/world/poi/PointOfInterestStorage;getTypesAndPositions(Ljava/util/function/Predicate;Ljava/util/function/Predicate;Lnet/minecraft/util/math/BlockPos;ILnet/minecraft/world/poi/PointOfInterestStorage$OccupationStatus;)Ljava/util/stream/Stream;"
            ),
            index = 3
    )
    private int modifyPoiSearchRadius(int originalRadius) {
        return 128;
    }
}
