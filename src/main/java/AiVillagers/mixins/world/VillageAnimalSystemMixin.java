package AiVillagers.mixins.world;

import AiVillagers.interfaces.AnimalEntityInterface;
import AiVillagers.interfaces.ButcherHelper;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.mob.MobEntity;
import net.minecraft.entity.passive.*;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.storage.ReadView;
import net.minecraft.storage.WriteView;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.math.Box;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.util.List;

@Mixin(AnimalEntity.class)
public abstract class VillageAnimalSystemMixin extends MobEntity implements AnimalEntityInterface {
    @Unique
    private boolean villageAnimal = false;

    protected VillageAnimalSystemMixin(EntityType<? extends PassiveEntity> type, World world) {
        super(type, world);
    }

    @Override
    public boolean aiVillagersFabric$isVillageAnimal() {
        return this.villageAnimal;
    }

    @Override
    public void aiVillagersFabric$setVillageAnimal(boolean value) {
        this.villageAnimal = value;
    }

    @Inject(method = "writeCustomData(Lnet/minecraft/storage/WriteView;)V", at = @At("TAIL"))
    private void onWriteCustomData(WriteView view, CallbackInfo ci) {
        if (this.villageAnimal) {
            view.putBoolean("Village", true);
        }
    }

    @Inject(method = "readCustomData(Lnet/minecraft/storage/ReadView;)V", at = @At("TAIL"))
    private void onReadCustomData(ReadView view, CallbackInfo ci) {
        this.villageAnimal = view.getBoolean("Village", false);
    }

    @Inject(method = "tickMovement()V", at = @At("TAIL"))
    private void aiVillagers_onTickMovement(CallbackInfo ci) {
        AnimalEntity self = (AnimalEntity)(Object)this;
        AnimalEntityInterface access = (AnimalEntityInterface)self;

        if ((self instanceof CowEntity || self instanceof PigEntity || self instanceof SheepEntity)
                && !access.aiVillagersFabric$isVillageAnimal()
                && self.hasCustomName()) {
            access.aiVillagersFabric$setVillageAnimal(true);
        }
    }

    @Inject(method = "interactMob(Lnet/minecraft/entity/player/PlayerEntity;Lnet/minecraft/util/Hand;)Lnet/minecraft/util/ActionResult;", at = @At("HEAD"))
    public void onInteractMob(PlayerEntity player, Hand hand, CallbackInfoReturnable<ActionResult> cir) {
        ItemStack stack = player.getStackInHand(hand);
        if (stack.isEmpty()) return;

        AnimalEntity animal = (AnimalEntity)(Object)this;
        if (!animal.isBreedingItem(stack)) return;
        if (this.getWorld().isClient) return;

        List<VillagerEntity> villagers = this.getWorld().getEntitiesByClass(
                VillagerEntity.class,
                new Box(this.getX()-10, this.getY()-10, this.getZ()-10, this.getX()+10, this.getY()+10, this.getZ()+10),
                v -> v instanceof ButcherHelper.ButcherAccess
        );

        ButcherHelper.ButcherAccess butcher = null;
        double closestDist = Double.MAX_VALUE;
        for (VillagerEntity villager : villagers) {
            double dist = villager.squaredDistanceTo(this);
            if (dist < closestDist) {
                closestDist = dist;
                butcher = (ButcherHelper.ButcherAccess) villager;
            }
        }

        if (butcher != null) {
            butcher.aiVillagersFabric$getLastFeedingTimestamps().put(this.getUuid(), this.getWorld().getTime());
            butcher.aiVillagersFabric$getFedAnimals().add(animal);
        }
    }
}