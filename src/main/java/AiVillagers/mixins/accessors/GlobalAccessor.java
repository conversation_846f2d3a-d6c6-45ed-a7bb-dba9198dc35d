package AiVillagers.mixins.accessors;

import net.minecraft.entity.passive.VillagerEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.gen.Accessor;
import org.spongepowered.asm.mixin.gen.Invoker;

@Mixin(VillagerEntity.class)
public interface GlobalAccessor {
    @Accessor("experience")
    int getExperience();
    @Accessor("experience")
    void setExperience(int xp);

    @Invoker("canLevelUp")
    boolean invokeCanLevelUp();

    @Invoker("levelUp")
    void invokeLevelUp();
}