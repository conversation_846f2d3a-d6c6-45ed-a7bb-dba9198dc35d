package AiVillagers.mixins.protection;

import net.minecraft.block.BlockState;
import net.minecraft.block.FarmlandBlock;
import net.minecraft.entity.Entity;
import net.minecraft.entity.damage.DamageSource;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(FarmlandBlock.class)
public abstract class FarmlandVillagerProtectionMixin {

    @Inject(method = "onLandedUpon(Lnet/minecraft/world/World;Lnet/minecraft/block/BlockState;Lnet/minecraft/util/math/BlockPos;Lnet/minecraft/entity/Entity;D)V", at = @At("HEAD"), cancellable = true)
    public void protectFarmlandFromVillagers(World world, BlockState state, BlockPos pos, Entity entity, double fallDistance, CallbackInfo callbackInfo) {
        if (entity instanceof VillagerEntity villager) {
            callbackInfo.cancel();
            applyFallDamageToVillager(villager, fallDistance);
        }
    }

    @Unique
    private void applyFallDamageToVillager(VillagerEntity villager, double fallDistance) {
        if (fallDistance > 3.0) {
            float damage = (float) (fallDistance - 3.0);

            damage = Math.min(damage, villager.getMaxHealth() * 0.8f);

                if (villager.getWorld() instanceof net.minecraft.server.world.ServerWorld serverWorld) {
                    DamageSource fallDamageSource = villager.getDamageSources().fall();
                    villager.damage(serverWorld, fallDamageSource, damage);
                }
            }
        }
    }