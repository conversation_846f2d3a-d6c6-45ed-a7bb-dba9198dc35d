package AiVillagers.mixins.protection;

import AiVillagers.interfaces.GlobalHelper;
import net.minecraft.entity.ai.brain.MemoryModuleType;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.sound.SoundEvents;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.GlobalPos;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Mixin(VillagerEntity.class)
public final class VillagerBedProtectionMixin {

    @Inject(method = "tick()V", at = @At("HEAD"))
    private void onTick(final CallbackInfo callbackInfo) {
        final var villager = (VillagerEntity) (Object) this;

        if (isClientSide(villager)) {
            return;
        }

        enforceVillagerBedProtection(villager);
    }

    @Unique
    private static boolean isClientSide(final VillagerEntity villager) {
        return switch (Objects.requireNonNull(villager, "Villager cannot be null")) {
            case VillagerEntity villagerEntity when villagerEntity.getWorld().isClient -> true;
            case VillagerEntity _ -> false;
        };
    }

    @Unique
    private static void enforceVillagerBedProtection(final VillagerEntity villager) {
        final Optional<GlobalPos> villagerHomePosition = getVillagerHomePosition(villager);

        switch (villagerHomePosition) {
            case Optional<GlobalPos> optionalGlobalPos when optionalGlobalPos.isEmpty() -> {
            }
            case Optional<GlobalPos> optionalGlobalPos -> {
                final GlobalPos homePosition = optionalGlobalPos.get();
                if (isInDifferentDimension(villager, homePosition)) {
                    return;
                }

                final BlockPos bedPosition = homePosition.pos();
                final List<? extends PlayerEntity> worldPlayers = getWorldPlayers(villager);

                final var context = new GlobalHelper.BedProtectionContext(villager, bedPosition, worldPlayers);
                processProtectionContext(context);
            }
        }
    }

    @Unique
    private static Optional<GlobalPos> getVillagerHomePosition(final VillagerEntity villager) {
        return villager.getBrain().getOptionalMemory(MemoryModuleType.HOME);
    }

    @Unique
    private static boolean isInDifferentDimension(final VillagerEntity villager, final GlobalPos homePosition) {
        return !homePosition.dimension().equals(villager.getWorld().getRegistryKey());
    }

    @Unique
    private static List<? extends PlayerEntity> getWorldPlayers(final VillagerEntity villager) {
        final World world = villager.getWorld();
        return world.getPlayers();
    }

    @Unique
    private static void processProtectionContext(final GlobalHelper.BedProtectionContext context) {
        final var villagerEntity = context.villager();
        final var bedPosition = context.bedPosition();
        final var players = context.players();

        players.stream()
            .filter(playerEntity -> isPlayerOccupyingBed(playerEntity, bedPosition))
            .findFirst()
            .ifPresent(playerEntity -> ejectPlayerFromBed(playerEntity, villagerEntity));
    }

    @Unique
    private static boolean isPlayerOccupyingBed(final PlayerEntity playerEntity, final BlockPos bedPosition) {
        return switch (playerEntity) {
            case PlayerEntity playerEntityCase when !playerEntityCase.isSleeping() -> false;
            case PlayerEntity playerEntityCase -> playerEntityCase.getSleepingPosition()
                .map(blockPos -> blockPos.equals(bedPosition))
                .orElse(false);
        };
    }

    @Unique
    private static void ejectPlayerFromBed(final PlayerEntity player, final VillagerEntity villager) {
        player.wakeUp(GlobalHelper.SleepProtectionState.SILENT_WAKE, GlobalHelper.SleepProtectionState.NO_UPDATE_SLEEPING_POSITION);

        switch (villager.getWorld()) {
            case World world when !world.isClient -> {
                final var volume = Math.clamp(1.0F, 0.1F, 2.0F);
                final var pitch = Math.clamp(1.0F, 0.5F, 1.5F);
                villager.playSound(SoundEvents.ENTITY_VILLAGER_NO, volume, pitch);
            }
            case World ignoredWorld -> {
            }
        }
    }
}