package AiVillagers.mixins.behavior;

import AiVillagers.interfaces.VillagerGenderProviderInterface;
import net.minecraft.entity.EntityData;
import net.minecraft.entity.SpawnReason;
import net.minecraft.entity.data.DataTracker;
import net.minecraft.entity.data.TrackedData;
import net.minecraft.entity.data.TrackedDataHandlerRegistry;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.storage.ReadView;
import net.minecraft.storage.WriteView;
import net.minecraft.world.LocalDifficulty;
import net.minecraft.world.ServerWorldAccess;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(VillagerEntity.class)
public abstract class VillagerGenderSystemMixin implements VillagerGenderProviderInterface {

    @Unique
    private static final TrackedData<Boolean> FEMALE =
            DataTracker.registerData(VillagerEntity.class, TrackedDataHandlerRegistry.BOOLEAN);

    @Inject(method = "initDataTracker", at = @At("TAIL"))
    private void initDataTracker(DataTracker.Builder builder, CallbackInfo ci) {
        builder.add(FEMALE, false);
    }

    @Inject(method = "initialize", at = @At("TAIL"))
    private void initialize(ServerWorldAccess world, LocalDifficulty difficulty, SpawnReason spawnReason,
                            EntityData entityData, CallbackInfoReturnable<EntityData> cir) {
        VillagerEntity self = (VillagerEntity)(Object)this;
        if (!world.isClient()) {
            boolean isFemale = world.getRandom().nextBoolean();
            self.getDataTracker().set(FEMALE, isFemale);
        }
    }

    @Inject(method = "writeCustomData", at = @At("TAIL"))
    private void writeCustomData(WriteView view, CallbackInfo ci) {
        VillagerEntity self = (VillagerEntity)(Object)this;
        boolean female = self.getDataTracker().get(FEMALE);
        view.putBoolean("Female", female);
    }

    @Inject(method = "readCustomData", at = @At("TAIL"))
    private void readCustomData(ReadView view, CallbackInfo ci) {
        VillagerEntity self = (VillagerEntity)(Object)this;
        boolean val = view.getBoolean("Female", false);
        self.getDataTracker().set(FEMALE, val);
    }

    @Unique
    @Override
    public boolean aiVillagersFabric$isFemale() {
        VillagerEntity self = (VillagerEntity)(Object)this;
        return self.getDataTracker().get(FEMALE);
    }
}