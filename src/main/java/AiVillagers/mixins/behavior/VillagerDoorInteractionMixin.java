package AiVillagers.mixins.behavior;

import AiVillagers.interfaces.BlockRecord;
import AiVillagers.interfaces.BlockType;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.DoorBlock;
import net.minecraft.block.FenceGateBlock;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.ai.pathing.Path;
import net.minecraft.entity.mob.PathAwareEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.EnumMap;
import java.util.Map;

@Mixin(VillagerEntity.class)
public abstract class VillagerDoorInteractionMixin extends PathAwareEntity {

    @Unique
    private final Map<BlockType, BlockRecord> lastInteractedBlocks = new EnumMap<>(BlockType.class);

    @Unique
    private BlockPos previousVillagerPos;

    protected VillagerDoorInteractionMixin(EntityType<? extends PathAwareEntity> type, World world) {
        super(type, world);
    }

    @Inject(method = "mobTick(Lnet/minecraft/server/world/ServerWorld;)V", at = @At("HEAD"))
    private void onMobTick(ServerWorld world, CallbackInfo ci) {
        VillagerEntity villager = (VillagerEntity) (Object) this;
        BlockPos currentPos = villager.getBlockPos();

        if (currentPos.equals(previousVillagerPos)) return;
        previousVillagerPos = currentPos;

        Path path = getNavigation().getCurrentPath();
        if (path != null && !path.isFinished()) {
            BlockPos pathPos = path.getNodePos(path.getCurrentNodeIndex());
            if (world.isChunkLoaded(pathPos.getX() >> 4, pathPos.getZ() >> 4)) {
                processBlockAt(world, villager, pathPos, world.getTime());
            }
            checkNearbyGates(world, villager, currentPos, world.getTime());
            checkNearbyDoors(world, villager, currentPos, world.getTime());
        }

        closePastBlocksIfFar(world, villager, world.getTime());
    }

    @Unique
    private void processBlockAt(ServerWorld world, VillagerEntity villager, BlockPos pos, long tick) {
        BlockState state = world.getBlockState(pos);
        Block block = state.getBlock();
        Vec3d center = Vec3d.ofCenter(pos);
        double distSq = villager.squaredDistanceTo(center);

        if (block instanceof FenceGateBlock) {
            BlockType type = BlockType.FENCE;
            BlockRecord record = lastInteractedBlocks.computeIfAbsent(type, _ -> new BlockRecord());
            boolean isOpen = state.get(type.openProp());

            if (!isOpen && distSq <= 1 && record.canAct(tick)) {
                world.setBlockState(pos, state.with(type.openProp(), true), 3);
                world.playSound(null, pos, type.openSound(), villager.getSoundCategory(), 1f, 1f);
                record.record(pos, tick);
            } else if (isOpen && distSq <= 1) {
                record.record(pos, tick);
            }

        } else if (block instanceof DoorBlock) {
            BlockType type = BlockType.DOOR;
            BlockRecord record = lastInteractedBlocks.computeIfAbsent(type, _ -> new BlockRecord());
            boolean isOpen = state.get(type.openProp());

            if (isOpen && distSq <= 1) {
                record.record(pos, tick);
            }
        }
    }

    @Unique
    private void checkNearbyGates(ServerWorld world, VillagerEntity villager, BlockPos currentPos, long tick) {
        BlockType type = BlockType.FENCE;
        BlockRecord record = lastInteractedBlocks.computeIfAbsent(type, _ -> new BlockRecord());

        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    BlockPos pos = currentPos.add(x, y, z);
                    BlockState state = world.getBlockState(pos);
                    Block block = state.getBlock();

                    if (block instanceof FenceGateBlock) {
                        Vec3d center = Vec3d.ofCenter(pos);
                        double distSq = villager.squaredDistanceTo(center);
                        boolean isOpen = state.get(type.openProp());

                        if (!isOpen && distSq <= 1 && record.canAct(tick)) {
                            world.setBlockState(pos, state.with(type.openProp(), true), 3);
                            world.playSound(null, pos, type.openSound(), villager.getSoundCategory(), 1f, 1f);
                            record.record(pos, tick);
                        } else if (isOpen && distSq <= 1) {
                            record.record(pos, tick);
                        }
                    }
                }
            }
        }
    }

    @Unique
    private void checkNearbyDoors(ServerWorld world, VillagerEntity villager, BlockPos currentPos, long tick) {
        BlockType type = BlockType.DOOR;
        BlockRecord record = lastInteractedBlocks.computeIfAbsent(type, _ -> new BlockRecord());

        for (int x = -1; x <= 1; x++) {
            for (int y = -1; y <= 1; y++) {
                for (int z = -1; z <= 1; z++) {
                    BlockPos pos = currentPos.add(x, y, z);
                    BlockState state = world.getBlockState(pos);
                    Block block = state.getBlock();

                    if (block instanceof DoorBlock) {
                        Vec3d center = Vec3d.ofCenter(pos);
                        double distSq = villager.squaredDistanceTo(center);
                        boolean isOpen = state.get(type.openProp());

                        if (isOpen && distSq <= 1) {
                            record.record(pos, tick);
                        }
                    }
                }
            }
        }
    }

    @Unique
    private void closePastBlocksIfFar(ServerWorld world, VillagerEntity villager, long tick) {
        for (BlockType type : BlockType.values()) {
            BlockRecord record = lastInteractedBlocks.get(type);
            if (record == null || record.getPos() == null) continue;
            double distSq = villager.squaredDistanceTo(Vec3d.ofCenter(record.getPos()));
            if (distSq > 1 && record.canAct(tick)) {
                BlockState state = world.getBlockState(record.getPos());
                if ((state.getBlock() instanceof FenceGateBlock || state.getBlock() instanceof DoorBlock) && state.get(type.openProp())) {
                    world.setBlockState(record.getPos(), state.with(type.openProp(), false), 3);
                    world.playSound(null, record.getPos(), type.closeSound(), villager.getSoundCategory(), 1f, 1f);
                    record.clear();
                }
            }
        }
    }
}