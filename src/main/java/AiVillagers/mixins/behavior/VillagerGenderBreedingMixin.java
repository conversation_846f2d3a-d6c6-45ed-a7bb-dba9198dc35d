package AiVillagers.mixins.behavior;

import AiVillagers.interfaces.VillagerGenderProviderInterface;
import net.minecraft.entity.ai.brain.Brain;
import net.minecraft.entity.ai.brain.MemoryModuleType;
import net.minecraft.entity.ai.brain.task.VillagerBreedTask;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.server.world.ServerWorld;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import java.util.Objects;
import java.util.Optional;

@Mixin(VillagerBreedTask.class)
public abstract class VillagerGenderBreedingMixin {

    @Inject(method = "run(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/passive/VillagerEntity;J)V", at = @At("HEAD"), cancellable = true)
    private void onRun(ServerWorld world, VillagerEntity villager, long time, CallbackInfo ci) {
        Brain<?> brain = villager.getBrain();

        Optional<VillagerEntity> potentialMateOpt = Objects.requireNonNull(brain.getOptionalMemory(MemoryModuleType.BREED_TARGET))
                .filter(e -> e instanceof VillagerEntity)
                .map(e -> (VillagerEntity) e);

        if (potentialMateOpt.isPresent()) {
            VillagerEntity mate = potentialMateOpt.get();
            boolean isVillagerFemale = ((VillagerGenderProviderInterface) villager).aiVillagersFabric$isFemale();
            boolean isMateFemale = ((VillagerGenderProviderInterface) mate).aiVillagersFabric$isFemale();

            if (isVillagerFemale == isMateFemale) {
                ci.cancel();
            }
        }
    }
}
