package AiVillagers.mixins.behavior;

import AiVillagers.interfaces.AnimalEntityInterface;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.DoorBlock;
import net.minecraft.block.FenceGateBlock;
import net.minecraft.entity.EntityType;
import net.minecraft.entity.ai.pathing.LandPathNodeMaker;
import net.minecraft.entity.ai.pathing.PathContext;
import net.minecraft.entity.ai.pathing.PathNodeType;
import net.minecraft.entity.mob.MobEntity;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.BlockView;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(LandPathNodeMaker.class)
public class EntityPathBehaviorMixin {

    @Inject(
            method = "getNodeType(Lnet/minecraft/entity/ai/pathing/PathContext;IIILnet/minecraft/entity/mob/MobEntity;)Lnet/minecraft/entity/ai/pathing/PathNodeType;",
            at = @At("HEAD"),
            cancellable = true
    )
    private void onGetNodeType(PathContext context, int x, int y, int z, MobEntity mob, CallbackInfoReturnable<PathNodeType> cir) {
        EntityType<?> type = mob.getType();
        BlockPos pos = new BlockPos(x, y, z);
        BlockView world = context.getWorld();
        BlockState state = world.getBlockState(pos);
        Block block = state.getBlock();

        if (type == EntityType.VILLAGER && block instanceof FenceGateBlock) {
            cir.setReturnValue(PathNodeType.WALKABLE_DOOR);
            return;
        }

        if ((type == EntityType.COW || type == EntityType.SHEEP || type == EntityType.PIG || type == EntityType.CHICKEN)
                && mob instanceof AnimalEntity animal) {
            AnimalEntityInterface access = (AnimalEntityInterface) animal;
            if (access.aiVillagersFabric$isVillageAnimal()) {
                if (block instanceof FenceGateBlock || block instanceof DoorBlock) {
                    cir.setReturnValue(PathNodeType.BLOCKED);
                }
            }
        }
    }
}