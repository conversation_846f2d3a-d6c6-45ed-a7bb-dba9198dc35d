package AiVillagers.mixins.behavior;

import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.registry.tag.ItemTags;
import net.minecraft.registry.tag.TagKey;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.village.VillagerProfession;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Mixin(VillagerEntity.class)
public class VillagerItemCollectionMixin {

    @Unique
    private static final Map<Item, Integer> ITEM_FOOD_VALUES = Map.of();

    @Inject(method = "canGather(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/item/ItemStack;)Z",
            at = @At("HEAD"),
            cancellable = true)
    private void onCanGather(ServerWorld world, ItemStack stack, CallbackInfoReturnable<Boolean> cir) {
        VillagerEntity villager = (VillagerEntity)(Object)this;
        RegistryEntry<VillagerProfession> professionEntry = villager.getVillagerData().profession();
        Item item = stack.getItem();

        if (item == Items.GOLD_NUGGET
                || item == Items.DANDELION
                || item == Items.POPPY
                || item == Items.APPLE
                || item == Items.BOOK
                || item == Items.OAK_SAPLING
                || item == Items.FEATHER) {
            cir.setReturnValue(true);
            return;
        }

        if (professionEntry.matchesKey(VillagerProfession.BUTCHER)) {
            if (item == Items.PORKCHOP
                    || item == Items.BEEF
                    || item == Items.MUTTON
                    || item == Items.COAL
                    || item == Items.LEATHER) {
                cir.setReturnValue(true);
                return;
            }
        }

        if (professionEntry.matchesKey(VillagerProfession.NITWIT)) {
            if (item == Items.BONE_MEAL) {
                cir.setReturnValue(true);
                return;
            }
        }

        if (professionEntry.matchesKey(VillagerProfession.SHEPHERD)) {
            TagKey<Item> woolTag = ItemTags.WOOL;
            if (stack.isIn(woolTag)) {
                cir.setReturnValue(true);
            }
        }
    }

    @Inject(method = "<clinit>", at = @At("TAIL"))
    private static void addAppleFoodValue(CallbackInfo ci) {
        try {
            Field field = VillagerEntity.class.getDeclaredField("ITEM_FOOD_VALUES");
            field.setAccessible(true);
            @SuppressWarnings("unchecked")
            Map<Item, Integer> map = (Map<Item, Integer>) field.get(null);

            if (!(map instanceof HashMap)) {
                Map<Item, Integer> modifiable = new HashMap<>(map);
                modifiable.put(Items.APPLE, 4);
                field.set(null, Collections.unmodifiableMap(modifiable));
            } else {
                map.put(Items.APPLE, 4);
            }

        } catch (NoSuchFieldException | IllegalAccessException illegalAccessException) {
            illegalAccessException.printStackTrace();
        }
    }
}