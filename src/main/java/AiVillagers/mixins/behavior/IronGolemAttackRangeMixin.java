package AiVillagers.mixins.behavior;

import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Redirect;

/**
 * Mixin que modifica el rango de ataque del Golem de Hierro para permitir ataques a 3 bloques de distancia
 * Intercepta la expansión del AttackBox directamente en IronGolemEntity
 * ya que hereda getAttackBox() de MobEntity a través de: IronGolemEntity → GolemEntity → PathAwareEntity → MobEntity
 */
@Mixin(IronGolemEntity.class)
public class IronGolemAttackRangeMixin {

    /**
     * Intercepta la expansión del AttackBox para extender el rango de ataque a 3 bloques
     * Como el mixin está en IronGolemEntity, solo afecta a los Iron Golems
     */
    @Redirect(
            method = "getAttackBox",
            at = @At(value = "INVOKE", target = "Lnet/minecraft/util/math/Box;expand(DDD)Lnet/minecraft/util/math/Box;")
    )
    private Box extendAttackBox(Box box, double x, double y, double z) {
        // Expandir el AttackBox a 3 bloques en lugar del rango normal (ATTACK_RANGE)
        // x y z originales son ATTACK_RANGE (~1.03), pero los reemplazamos con 3.0
        return box.expand(3.0, y, 3.0); // y se mantiene como 0.0 (sin expansión vertical)
    }
}
