package AiVillagers.mixins.behavior;

import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Redirect;

/**
 * Mixin que modifica el rango de ataque del Golem de Hierro para permitir ataques a 3 bloques de distancia
 * Intercepta la expansión del AttackBox en lugar de modificar la constante ATTACK_RANGE
 */
@Mixin(IronGolemEntity.class)
public class IronGolemAttackRangeMixin {

    /**
     * Intercepta la expansión del AttackBox para extender el rango de ataque a 3 bloques
     * Este es el método correcto ya que getAttackBox() usa ATTACK_RANGE para expandir la caja de colisión
     */
    @Redirect(
            method = "getAttackBox",
            at = @At(value = "INVOKE", target = "Lnet/minecraft/util/math/Box;expand(DDD)Lnet/minecraft/util/math/Box;")
    )
    private Box extendAttackBox(Box box, double x, double y, double z) {
        // Expandir el AttackBox a 3 bloques en lugar del rango normal (ATTACK_RANGE)
        // x y z originales son ATTACK_RANGE, pero los reemplazamos con 3.0
        return box.expand(3.0, y, 3.0); // y se mantiene como 0.0 (sin expansión vertical)
    }
}
