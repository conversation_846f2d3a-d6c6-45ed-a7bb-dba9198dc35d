package AiVillagers.mixins.behavior;

import net.minecraft.entity.mob.MobEntity;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Redirect;

/**
 * Mixin que modifica el rango de ataque del Golem de Hierro para permitir ataques a 3 bloques de distancia
 * Intercepta la expansión del AttackBox en MobEntity ya que getAttackBox() está definido ahí
 */
@Mixin(MobEntity.class)
public class IronGolemAttackRangeMixin {

    /**
     * Intercepta la expansión del AttackBox para extender el rango de ataque a 3 bloques
     * Solo aplica la modificación si la entidad es un Iron Golem
     */
    @Redirect(
            method = "getAttackBox",
            at = @At(value = "INVOKE", target = "Lnet/minecraft/util/math/Box;expand(DDD)Lnet/minecraft/util/math/Box;")
    )
    private Box extendAttackBox(Box box, double x, double y, double z) {
        // Obtener la entidad actual usando el cast de mixin
        MobEntity entity = (MobEntity) (Object) this;

        // Solo modificar el rango de ataque para Iron Golems
        if (entity instanceof IronGolemEntity) {
            // Expandir el AttackBox a 3 bloques en lugar del rango normal (ATTACK_RANGE)
            return box.expand(3.0, y, 3.0); // y se mantiene como 0.0 (sin expansión vertical)
        }

        // Para otras entidades, usar el comportamiento original
        return box.expand(x, y, z);
    }
}
