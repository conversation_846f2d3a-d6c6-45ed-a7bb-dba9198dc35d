package AiVillagers.mixins.behavior;

import net.minecraft.entity.mob.MobEntity;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Redirect;

@Mixin(MobEntity.class)
public class IronGolemAttackRangeMixin {

    @Redirect(
            method = "getAttackBox()Lnet/minecraft/util/math/Box;",
            at = @At(value = "INVOKE", target = "Lnet/minecraft/util/math/Box;expand(DDD)Lnet/minecraft/util/math/Box;")
    )
    private Box extendAttackBox(Box box, double x, double y, double z) {
        MobEntity entity = (MobEntity) (Object) this;

        if (entity instanceof IronGolemEntity) {
            System.out.println("Iron Golem Attack Range - Original: x=" + x + ", y=" + y + ", z=" + z);
            System.out.println("Iron Golem Attack Range - Modified: x=3.0, y=1.0, z=3.0");

            return box.expand(1.56, 1.36, 1.56);
        }

        return box.expand(x, y, z);
    }
}
