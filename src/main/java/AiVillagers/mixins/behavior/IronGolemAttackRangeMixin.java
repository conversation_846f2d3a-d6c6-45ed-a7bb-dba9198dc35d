package AiVillagers.mixins.behavior;

import AiVillagers.goals.ExtendedRangeIronGolemAttackGoal;
import AiVillagers.mixins.accessors.IronGolemAccessor;
import net.minecraft.entity.passive.IronGolemEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import static AiVillagers.core.AiVillagersMod.LOGGER;

@Mixin(IronGolemEntity.class)
public class IronGolemAttackRangeMixin {

    @Inject(method = "initGoals", at = @At("TAIL"))
    private void addExtendedAttackRange(CallbackInfo ci) {
        IronGolemEntity ironGolem = (IronGolemEntity) (Object) this;
        IronGolemAccessor accessor = (IronGolemAccessor) ironGolem;

        accessor.getGoalSelector().add(1, new ExtendedRangeIronGolemAttackGoal(ironGolem, 1.0, true));
        
        LOGGER.info("Rango de ataque extendido agregado al Golem de Hierro (3 bloques)");
    }
}
