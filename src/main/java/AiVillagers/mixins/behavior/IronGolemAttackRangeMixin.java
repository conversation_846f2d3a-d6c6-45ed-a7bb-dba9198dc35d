package AiVillagers.mixins.behavior;

import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.ai.goal.Goal;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.server.world.ServerWorld;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import static AiVillagers.core.AiVillagersMod.LOGGER;

@Mixin(IronGolemEntity.class)
public class IronGolemAttackRangeMixin {

    @Shadow
    protected net.minecraft.entity.ai.goal.GoalSelector goalSelector;

    @Unique
    private static final double EXTENDED_ATTACK_RANGE = 3.0;

    @Inject(method = "initGoals", at = @At("TAIL"))
    private void addExtendedAttackRange(CallbackInfo ci) {
        IronGolemEntity ironGolem = (IronGolemEntity) (Object) this;

        this.goalSelector.add(1, new ExtendedRangeAttackGoal(ironGolem));

        LOGGER.info("Rango de ataque extendido agregado al Golem de Hierro (3 bloques)");
    }

    private static class ExtendedRangeAttackGoal extends Goal {
        private final IronGolemEntity ironGolem;
        private final double speed;
        private int attackCooldown;

        public ExtendedRangeAttackGoal(IronGolemEntity mob) {
            this.ironGolem = mob;
            this.speed = 1.0;
            this.attackCooldown = 0;
        }

        @Override
        public boolean canStart() {
            LivingEntity target = this.ironGolem.getTarget();
            if (target == null || !target.isAlive()) {
                return false;
            }

            double distanceToTarget = this.ironGolem.squaredDistanceTo(target);
            double maxAttackDistanceSquared = EXTENDED_ATTACK_RANGE * EXTENDED_ATTACK_RANGE;

            return distanceToTarget <= maxAttackDistanceSquared;
        }

        @Override
        public boolean shouldContinue() {
            LivingEntity target = this.ironGolem.getTarget();
            if (target == null || !target.isAlive()) {
                return false;
            }

            double distanceToTarget = this.ironGolem.squaredDistanceTo(target);
            double maxAttackDistanceSquared = EXTENDED_ATTACK_RANGE * EXTENDED_ATTACK_RANGE;

            return distanceToTarget <= maxAttackDistanceSquared * 1.5; // Un poco más de tolerancia para continuar
        }

        @Override
        public void tick() {
            LivingEntity target = this.ironGolem.getTarget();
            if (target == null) {
                return;
            }

            double distanceToTarget = this.ironGolem.squaredDistanceTo(target);
            double maxAttackDistanceSquared = EXTENDED_ATTACK_RANGE * EXTENDED_ATTACK_RANGE;

            this.ironGolem.getLookControl().lookAt(target);

            if (distanceToTarget <= maxAttackDistanceSquared) {
                this.ironGolem.getNavigation().stop();

                if (this.attackCooldown <= 0) {
                    if (this.ironGolem.getWorld() instanceof ServerWorld serverWorld) {
                        this.ironGolem.tryAttack(serverWorld, target);
                        this.attackCooldown = 20;
                    }
                }
            } else {
                this.ironGolem.getNavigation().startMovingTo(target, this.speed);
            }

            if (this.attackCooldown > 0) {
                this.attackCooldown--;
            }
        }
    }
}
