package AiVillagers.mixins.behavior;

import net.minecraft.entity.Entity;
import net.minecraft.entity.passive.IronGolemEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Redirect;

/**
 * Mixin que modifica el rango de ataque del Golem de Hierro para permitir ataques a 3 bloques de distancia
 */
@Mixin(IronGolemEntity.class)
public class IronGolemAttackRangeMixin {

    /**
     * Intercepta la verificación de distancia en el método tryAttack
     * Cambia el rango de ataque de 1 bloque (por defecto) a 3 bloques
     */
    @Redirect(
            method = "tryAttack",
            at = @At(value = "INVOKE", target = "Lnet/minecraft/entity/passive/IronGolemEntity;squaredDistanceTo(Lnet/minecraft/entity/Entity;)D")
    )
    private double extendAttackRange(IronGolemEntity ironGolem, Entity target) {
        double actualDistance = ironGolem.squaredDistanceTo(target);

        // Si está dentro de 3 bloques, simular que está a distancia de ataque normal
        if (actualDistance <= 9.0) { // 3 bloques al cuadrado (3 * 3 = 9)
            return 1.0; // Simular distancia de 1 bloque para permitir el ataque
        }

        return actualDistance; // Devolver la distancia real si está muy lejos
    }
}
