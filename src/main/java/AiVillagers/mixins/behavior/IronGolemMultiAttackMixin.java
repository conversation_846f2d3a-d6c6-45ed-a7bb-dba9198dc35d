package AiVillagers.mixins.behavior;

import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.mob.Monster;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.util.List;

import static AiVillagers.core.AiVillagersMod.LOGGER;

@Mixin(IronGolemEntity.class)
public class IronGolemMultiAttackMixin {

    @Unique
    private boolean isProcessingMultiAttack = false;

    @Inject(method = "tryAttack", at = @At("RETURN"))
    private void multiTargetAttack(ServerWorld world, Entity target, CallbackInfoReturnable<Boolean> cir) {
        if (!cir.getReturnValue()) {
            return;
        }

        if (isProcessingMultiAttack) {
            return;
        }

        IronGolemEntity ironGolem = (IronGolemEntity) (Object) this;
        isProcessingMultiAttack = true;

        try {
            double searchRadius = 5.0;
            Box searchBox = ironGolem.getBoundingBox().expand(searchRadius);

            List<LivingEntity> nearbyEnemies = world.getEntitiesByClass(
                LivingEntity.class,
                searchBox,
                entity -> entity != ironGolem &&
                         entity != target &&
                         entity.isAlive() &&
                         ironGolem.canTarget(entity) &&
                         ironGolem.isInAttackRange(entity) &&
                         isHostileMob(entity) &&
                         isInFrontOfGolem(ironGolem, entity)
            );

            System.out.println("Iron Golem Multi-Attack: Found " + nearbyEnemies.size() + " additional enemies in attack range");

            for (LivingEntity enemy : nearbyEnemies) {
                try {
                    enemy.damage(world, ironGolem.getDamageSources().mobAttack(ironGolem), 7.0F); // Daño del Iron Golem
                    System.out.println("Iron Golem attacked: " + enemy.getType().getTranslationKey());
                } catch (Exception e) {
                    LOGGER.error("Error attacking enemy {}: {}", enemy.getType().getTranslationKey(), e.getMessage());
                }
            }
        } finally {
            isProcessingMultiAttack = false;
        }
    }

    @Unique
    private static boolean isHostileMob(LivingEntity livingEntity) {
        return livingEntity instanceof Monster || livingEntity instanceof HostileEntity;
    }

    @Unique
    private static boolean isInFrontOfGolem(IronGolemEntity ironGolem, LivingEntity target) {
        double golemYaw = Math.toRadians(ironGolem.getYaw());

        double golemDirX = -Math.sin(golemYaw);
        double golemDirZ = Math.cos(golemYaw);

        double toTargetX = target.getX() - ironGolem.getX();
        double toTargetZ = target.getZ() - ironGolem.getZ();

        double dotProduct = golemDirX * toTargetX + golemDirZ * toTargetZ;

        return dotProduct >= 0.0;
    }
}