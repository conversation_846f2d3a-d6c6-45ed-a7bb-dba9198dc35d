package AiVillagers.mixins.behavior;

import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.mob.Monster;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.util.List;

import static AiVillagers.core.AiVillagersMod.LOGGER;

@Mixin(IronGolemEntity.class)
public class IronGolemMultiAttackMixin {

    @Inject(method = "tryAttack", at = @At("HEAD"), cancellable = true)
    private void multiTargetAttack(ServerWorld world, Entity target, CallbackInfoReturnable<Boolean> cir) {
        IronGolemEntity ironGolem = (IronGolemEntity) (Object) this;

        double searchRadius = 5.0;
        Box searchBox = ironGolem.getBoundingBox().expand(searchRadius);

        List<LivingEntity> nearbyEnemies = world.getEntitiesByClass(
            LivingEntity.class, 
            searchBox, 
            entity -> entity != ironGolem &&
                     entity.isAlive() &&
                     ironGolem.canTarget(entity) &&
                     ironGolem.isInAttackRange(entity) &&
                     isHostileMob(entity) // Solo atacar mobs hostiles
        );
        
        System.out.println("Iron Golem Multi-Attack: Found " + nearbyEnemies.size() + " enemies in attack range");
        
        boolean attackedAny = false;

        for (LivingEntity enemy : nearbyEnemies) {
            try {
                boolean attackResult = ironGolem.tryAttack(world, enemy);
                if (attackResult) {
                    attackedAny = true;
                    System.out.println("Iron Golem attacked: " + enemy.getType().getTranslationKey());
                }
            } catch (Exception e) {
                LOGGER.error("Error attacking enemy {}: {}", enemy.getType().getTranslationKey(), e.getMessage());
            }
        }

        if (attackedAny) {
            cir.setReturnValue(true);
        }
    }

    @Unique
    private static boolean isHostileMob(LivingEntity entity) {
        if (entity instanceof Monster || entity instanceof HostileEntity) {
            return true;
        }

        String entityType = entity.getType().getTranslationKey();

        return entityType.contains("zombie") ||
               entityType.contains("skeleton") ||
               entityType.contains("spider") ||
               entityType.contains("witch") ||
               entityType.contains("enderman") ||
               entityType.contains("slime") ||
               entityType.contains("phantom") ||
               entityType.contains("pillager") ||
               entityType.contains("vindicator") ||
               entityType.contains("evoker");
    }
}
