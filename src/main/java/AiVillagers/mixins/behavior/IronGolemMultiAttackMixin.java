package AiVillagers.mixins.behavior;

import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.util.List;

import static AiVillagers.core.AiVillagersMod.LOGGER;

/**
 * Mixin que permite al Iron Golem atacar a múltiples objetivos dentro de su rango de ataque
 */
@Mixin(IronGolemEntity.class)
public class IronGolemMultiAttackMixin {

    /**
     * Intercepta el método tryAttack para permitir ataques múltiples
     */
    @Inject(method = "tryAttack", at = @At("HEAD"), cancellable = true)
    private void multiTargetAttack(ServerWorld world, LivingEntity target, CallbackInfoReturnable<Boolean> cir) {
        IronGolemEntity ironGolem = (IronGolemEntity) (Object) this;
        
        // Obtener la caja de ataque del golem
        Box attackBox = ironGolem.getAttackBox();
        
        // Buscar todas las entidades vivas hostiles dentro del rango de ataque
        List<LivingEntity> nearbyEnemies = world.getEntitiesByClass(
            LivingEntity.class, 
            attackBox, 
            entity -> entity != ironGolem && 
                     entity.isAlive() && 
                     ironGolem.canTarget(entity) &&
                     ironGolem.isInAttackRange(entity)
        );
        
        System.out.println("Iron Golem Multi-Attack: Found " + nearbyEnemies.size() + " enemies in range");
        
        boolean attackedAny = false;
        
        // Atacar a todas las entidades encontradas
        for (LivingEntity enemy : nearbyEnemies) {
            try {
                // Realizar el ataque original contra cada enemigo
                boolean attackResult = ironGolem.tryAttack(world, enemy);
                if (attackResult) {
                    attackedAny = true;
                    System.out.println("Iron Golem attacked: " + enemy.getType().getTranslationKey());
                }
            } catch (Exception e) {
                LOGGER.error("Error attacking enemy " + enemy.getType().getTranslationKey() + ": " + e.getMessage());
            }
        }
        
        // Si atacamos a alguien, cancelar el ataque original para evitar duplicados
        if (attackedAny) {
            cir.setReturnValue(true);
        }
    }
}
