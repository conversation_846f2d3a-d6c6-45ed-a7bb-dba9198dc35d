package AiVillagers.mixins.behavior;

import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.mob.HostileEntity;
import net.minecraft.entity.mob.Monster;
import net.minecraft.entity.passive.IronGolemEntity;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.util.math.Box;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.util.List;

import static AiVillagers.core.AiVillagersMod.LOGGER;

/**
 * Mixin que permite al Iron Golem atacar a múltiples objetivos dentro de su rango de ataque
 */
@Mixin(IronGolemEntity.class)
public class IronGolemMultiAttackMixin {

    @Unique
    private boolean isProcessingMultiAttack = false;

    /**
     * Intercepta el método tryAttack para permitir ataques múltiples
     */
    @Inject(method = "tryAttack", at = @At("RETURN"))
    private void multiTargetAttack(ServerWorld world, Entity target, CallbackInfoReturnable<Boolean> cir) {
        // Solo proceder si el ataque original fue exitoso
        if (!cir.getReturnValue()) {
            return;
        }

        // Evitar recursión infinita
        if (isProcessingMultiAttack) {
            return;
        }

        IronGolemEntity ironGolem = (IronGolemEntity) (Object) this;
        isProcessingMultiAttack = true;

        try {
            double searchRadius = 5.0;
            Box searchBox = ironGolem.getBoundingBox().expand(searchRadius);

            List<LivingEntity> nearbyEnemies = world.getEntitiesByClass(
                LivingEntity.class,
                searchBox,
                entity -> entity != ironGolem &&
                         entity != target && // Excluir el objetivo original ya atacado
                         entity.isAlive() &&
                         ironGolem.canTarget(entity) &&
                         ironGolem.isInAttackRange(entity) &&
                         isHostileMob(entity) // Solo atacar mobs hostiles
            );

            System.out.println("Iron Golem Multi-Attack: Found " + nearbyEnemies.size() + " additional enemies in attack range");

            for (LivingEntity enemy : nearbyEnemies) {
                try {
                    // Aplicar daño directamente sin pasar por tryAttack
                    enemy.damage(world, ironGolem.getDamageSources().mobAttack(ironGolem), 7.0F); // Daño del Iron Golem
                    System.out.println("Iron Golem attacked: " + enemy.getType().getTranslationKey());
                } catch (Exception e) {
                    LOGGER.error("Error attacking enemy {}: {}", enemy.getType().getTranslationKey(), e.getMessage());
                }
            }
        } finally {
            isProcessingMultiAttack = false;
        }
    }

    /**
     * Verifica si una entidad es un mob hostil que debería ser atacado por el Iron Golem
     */
    @Unique
    private static boolean isHostileMob(LivingEntity entity) {
        // Verificar si es una entidad hostil (Monster o HostileEntity)
        if (entity instanceof Monster || entity instanceof HostileEntity) {
            return true;
        }
        
        // Lista adicional de mobs específicos que deberían ser atacados
        String entityType = entity.getType().getTranslationKey();
        
        // Incluir algunos mobs que pueden no ser técnicamente "hostiles" pero que los golems deberían atacar
        return entityType.contains("zombie") || 
               entityType.contains("skeleton") || 
               entityType.contains("spider") || 
               entityType.contains("creeper") || 
               entityType.contains("witch") || 
               entityType.contains("enderman") ||
               entityType.contains("slime") ||
               entityType.contains("phantom") ||
               entityType.contains("pillager") ||
               entityType.contains("vindicator") ||
               entityType.contains("evoker");
    }
}
