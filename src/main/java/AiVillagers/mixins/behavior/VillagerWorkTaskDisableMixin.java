package AiVillagers.mixins.behavior;

import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.entity.ai.brain.task.VillagerWorkTask;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.server.world.ServerWorld;
import net.minecraft.village.VillagerProfession;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(VillagerWorkTask.class)
public class VillagerWorkTaskDisableMixin {

    @Inject(method = "shouldRun(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/passive/VillagerEntity;)Z", at = @At("HEAD"), cancellable = true)
    private void preventFishermanWork(ServerWorld world, VillagerEntity villager, CallbackInfoReturnable<Boolean> cir) {
        RegistryEntry<VillagerProfession> profession = villager.getVillagerData().profession();

            if (profession.matchesKey(VillagerProfession.FISHERMAN)) {
            cir.setReturnValue(false);
            }

            if (profession.matchesKey(VillagerProfession.BUTCHER)) {
                cir.setReturnValue(false);
            }
        }
    }