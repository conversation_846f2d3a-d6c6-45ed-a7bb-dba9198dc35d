package AiVillagers.mixins.core;

import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityType;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.ModifyArgs;
import org.spongepowered.asm.mixin.injection.invoke.arg.Args;

@Mixin(targets = "net.minecraft.server.world.ServerChunkLoadingManager$EntityTracker")
public abstract class ExperienceOrbTrackingMixin {

    @ModifyArgs(
            method = "<init>(Lnet/minecraft/server/world/ServerChunkLoadingManager;Lnet/minecraft/entity/Entity;IIZ)V",
            at = @At(
                    value = "INVOKE",
                    target = "Lnet/minecraft/server/network/EntityTrackerEntry;<init>(Lnet/minecraft/server/world/ServerWorld;Lnet/minecraft/entity/Entity;IZLjava/util/function/Consumer;Ljava/util/function/BiConsumer;)V"
            )
    )

    private void aiVillagers$modifyEntityTrackingParameters(Args args) {
        Entity entity = args.get(1);

        if (entity.getType() == EntityType.EXPERIENCE_ORB) {
            args.set(2, 1);
            args.set(3, true);
        }
    }
}