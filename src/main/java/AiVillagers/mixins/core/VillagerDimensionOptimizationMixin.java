package AiVillagers.mixins.core;

import net.minecraft.entity.EntityDimensions;
import net.minecraft.entity.EntityType;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import static java.util.Objects.requireNonNull;

@Mixin(EntityType.class)
public final class VillagerDimensionOptimizationMixin {

    @Unique @Final
    private static final ConcurrentHashMap<EntityType<?>, Optional<EntityDimensions>> dimensionsCache =
        new ConcurrentHashMap<>(16, 0.75f, 1);

    @Inject(method = "getDimensions", at = @At("RETURN"), cancellable = true)
    private void optimizeVillagerDimensions(final CallbackInfoReturnable<EntityDimensions> cir) {
        final var currentEntityType = (EntityType<?>)(Object)this;
        if (!isVillagerEntityType(currentEntityType)) {
            return;
        }

        final var originalDimensions = requireNonNull(cir.getReturnValue());
        final var cachedDimensions = getCachedOrComputeDimensions(currentEntityType, originalDimensions);

        cir.setReturnValue(cachedDimensions);
    }

    @Unique
    private static boolean isVillagerEntityType(final EntityType<?> entityType) {
        return switch (entityType) {
            case EntityType<?> type when type == EntityType.VILLAGER -> true;
            default -> false;
        };
    }

    @Unique
    private static EntityDimensions getCachedOrComputeDimensions(
            final EntityType<?> entityType,
            final EntityDimensions originalDimensions) {

        return dimensionsCache.computeIfAbsent(entityType,
                        _ -> Optional.of(computeOptimizedDimensions(originalDimensions)))
            .orElseThrow();
    }

    @Unique
    private static EntityDimensions computeOptimizedDimensions(final EntityDimensions original) {
        requireNonNull(original);
        final var originalWidth = Math.max(0.1f, original.width());
        return EntityDimensions.changing(originalWidth, Math.clamp(1.90f, 0.5f, 3.0f));
    }
}