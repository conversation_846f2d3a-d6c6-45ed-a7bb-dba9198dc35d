package AiVillagers.core;

import AiVillagers.rendering.entity.VillagerFishingBobberEntityRenderer;
import AiVillagers.entities.ModEntities;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.rendering.v1.EntityRendererRegistry;

import static AiVillagers.core.AiVillagersMod.LOGGER;

public class AiVillagersClient implements ClientModInitializer {

	@Override
	public void onInitializeClient() {
		LOGGER.info("Inicializando cliente de AiVillagers");

		EntityRendererRegistry.register(ModEntities.VILLAGER_FISHING_BOBBER,
				VillagerFishingBobberEntityRenderer::new);

		LOGGER.info("Cliente de AiVillagers inicializado correctamente");
	}
}