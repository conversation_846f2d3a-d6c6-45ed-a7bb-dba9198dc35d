package AiVillagers.mixins.client.FisherMixins;

import AiVillagers.interfaces.HolderEntityStateInterface;
import net.minecraft.client.render.entity.state.ItemHolderEntityRenderState;
import net.minecraft.client.item.ItemModelManager;
import net.minecraft.entity.LivingEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ItemHolderEntityRenderState.class)
public class ItemHolderEntityRenderStateMixin implements HolderEntityStateInterface {

    @Unique private LivingEntity holderEntity;

    @Override
    public void aiVillagersFabric$setHolderEntity(LivingEntity entity) {
        this.holderEntity = entity;
    }

    @Override
    public LivingEntity aiVillagersFabric$getHolderEntity() {
        return this.holderEntity;
    }

    @Inject(method = "update", at = @At("HEAD"))
    private static void captureEntity(LivingEntity entity, ItemHolderEntityRenderState state, ItemModelManager itemModelManager, CallbackInfo ci) {
        ((HolderEntityStateInterface) state).aiVillagersFabric$setHolderEntity(entity);
    }
}
