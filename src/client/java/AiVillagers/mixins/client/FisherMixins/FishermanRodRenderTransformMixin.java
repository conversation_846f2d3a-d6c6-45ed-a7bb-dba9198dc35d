package AiVillagers.mixins.client.FisherMixins;

import AiVillagers.interfaces.HolderEntityStateInterface;
import AiVillagers.items.ModItems;
import net.minecraft.client.render.entity.feature.VillagerHeldItemFeatureRenderer;
import net.minecraft.client.render.entity.state.ItemHolderEntityRenderState;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.village.VillagerProfession;
import org.joml.Quaternionf;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(VillagerHeldItemFeatureRenderer.class)
public class FishermanRodRenderTransformMixin {

    @Inject(method = "applyTransforms(Lnet/minecraft/client/render/entity/state/ItemHolderEntityRenderState;Lnet/minecraft/client/util/math/MatrixStack;)V", at = @At("HEAD"))
    private void applyTransforms(ItemHolderEntityRenderState state, MatrixStack matrices, CallbackInfo ci) {

        if (state instanceof HolderEntityStateInterface duck) {
            if (duck.aiVillagersFabric$getHolderEntity() instanceof VillagerEntity villager &&
                    villager.getVillagerData() != null) {

                RegistryEntry<VillagerProfession> professionEntry = villager.getVillagerData().profession();
                ItemStack stack = villager.getMainHandStack();

                if (professionEntry.matchesKey(VillagerProfession.FISHERMAN) &&
                        stack.getItem() == ModItems.VILLAGER_FISHING_ROD_CAST) {

                    matrices.translate(0.63F, -0.13F, -0.63F);
                    float yRotationRadians = (float) Math.toRadians(90);
                    matrices.multiply(new Quaternionf().rotationXYZ(0.2F, yRotationRadians, 0));
                    matrices.scale(1.5F, 1.5F, 1.5F);
                }
            }
        }
    }
}