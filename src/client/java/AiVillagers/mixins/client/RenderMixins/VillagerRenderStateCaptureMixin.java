package AiVillagers.mixins.client.RenderMixins;

import AiVillagers.interfaces.VillagerReferenceHolderInterface;
import net.minecraft.client.render.entity.VillagerEntityRenderer;
import net.minecraft.client.render.entity.state.VillagerEntityRenderState;
import net.minecraft.entity.passive.VillagerEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

//
// Este Mixin modifica el comportamiento del renderizador de aldeanos (VillagerEntityRenderer).
// Inyecta logica al metodo updateRenderState para asociar la entidad Villager
// con su estado de render personalizado usando una interfaz (VillagerReferenceHolderInterface).
//

@Mixin(VillagerEntityRenderer.class)
public class VillagerRenderStateCaptureMixin {

    //
    // Inyecta logica al inicio del metodo updateRenderState.
    // La entidad Villager es capturada y almacenada dentro del render state
    // mediante la interfaz VillagerReferenceHolderInterface.
    //

    @Inject(method = "updateRenderState(Lnet/minecraft/entity/passive/VillagerEntity;Lnet/minecraft/client/render/entity/state/VillagerEntityRenderState;F)V", at = @At("HEAD"))
    private void aiVillagersFabric$injectVillager(VillagerEntity villager, VillagerEntityRenderState state, float tickDelta, CallbackInfo ci) {
        ((VillagerReferenceHolderInterface) state).aiVillagersFabric$setVillager(villager);
    }
}
