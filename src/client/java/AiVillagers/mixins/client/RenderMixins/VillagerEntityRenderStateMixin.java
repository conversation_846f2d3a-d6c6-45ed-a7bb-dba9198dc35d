package AiVillagers.mixins.client.RenderMixins;

import AiVillagers.interfaces.VillagerReferenceHolderInterface;
import net.minecraft.client.render.entity.state.VillagerEntityRenderState;
import net.minecraft.entity.passive.VillagerEntity;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Unique;

//
// Este Mixin añade un campo privado a VillagerEntityRenderState para almacenar
// una referencia a la entidad Villager asociada al estado de render.
// Implementa la interfaz VillagerReferenceHolderInterface para permitir acceder y modificar dicha referencia.
//

@Mixin(VillagerEntityRenderState.class)
public class VillagerEntityRenderStateMixin implements VillagerReferenceHolderInterface {

    @Unique
    private VillagerEntity aiVillagersFabric$villager;

    //
    // Guarda la entidad Villager dentro del estado de render.
    //

    @Override
    public void aiVillagersFabric$setVillager(VillagerEntity villager) {
        this.aiVillagersFabric$villager = villager;
    }

    //
    // Devuelve la entidad Villager almacenada en el estado de render.
    //

    @Override
    public VillagerEntity aiVillagersFabric$getVillager() {
        return this.aiVillagersFabric$villager;
    }
}
