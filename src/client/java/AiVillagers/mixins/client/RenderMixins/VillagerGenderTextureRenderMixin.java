package AiVillagers.mixins.client.RenderMixins;

import AiVillagers.interfaces.VillagerGenderProviderInterface;
import AiVillagers.interfaces.VillagerReferenceHolderInterface;
import net.minecraft.client.render.entity.VillagerEntityRenderer;
import net.minecraft.client.render.entity.state.VillagerEntityRenderState;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.util.Identifier;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(VillagerEntityRenderer.class)
public abstract class VillagerGenderTextureRenderMixin {

    @Inject(method = "getTexture(Lnet/minecraft/client/render/entity/state/VillagerEntityRenderState;)Lnet/minecraft/util/Identifier;", at = @At("HEAD"), cancellable = true)
    private void aiVillagersFabric$overrideTexture(VillagerEntityRenderState state, CallbackInfoReturnable<Identifier> cir) {
        VillagerEntity villager = ((VillagerReferenceHolderInterface) state).aiVillagersFabric$getVillager();
        if (villager != null && ((VillagerGenderProviderInterface) villager).aiVillagersFabric$isFemale()) {
            cir.setReturnValue(Identifier.of("ai_villagers", "textures/villagers/villager_fem_head.png"));
        }
    }
    @Inject(method = "updateRenderState(Lnet/minecraft/entity/passive/VillagerEntity;Lnet/minecraft/client/render/entity/state/VillagerEntityRenderState;F)V", at = @At("HEAD"))
    private void aiVillagersFabric$injectVillager(VillagerEntity villager, VillagerEntityRenderState state, float tickDelta, CallbackInfo ci) {
        ((VillagerReferenceHolderInterface) state).aiVillagersFabric$setVillager(villager);
    }
}