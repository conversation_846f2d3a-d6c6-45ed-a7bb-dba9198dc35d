package AiVillagers.rendering.entity;

import AiVillagers.entities.fishing.VillagerFishingBobberEntity;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.render.OverlayTexture;
import net.minecraft.client.render.RenderLayer;
import net.minecraft.client.render.VertexConsumer;
import net.minecraft.client.render.VertexConsumerProvider;
import net.minecraft.client.render.entity.EntityRenderer;
import net.minecraft.client.render.entity.EntityRendererFactory;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;

@Environment(EnvType.CLIENT)
public class VillagerFishingBobberEntityRenderer extends EntityRenderer<VillagerFishingBobberEntity, VillagerFishingBobberEntityState> {
    private static final Identifier TEXTURE = Identifier.ofVanilla("textures/entity/fishing_hook.png");
    private static final RenderLayer LAYER = RenderLayer.getEntityCutout(TEXTURE);

    public VillagerFishingBobberEntityRenderer(EntityRendererFactory.Context context) {
        super(context);
    }

    public boolean shouldRender(VillagerFishingBobberEntity villagerFishingBobberEntity, net.minecraft.client.render.Frustum frustum, double d, double e, double f) {
        boolean hasOwner = villagerFishingBobberEntity.getBobberOwner() != null;
        boolean shouldRenderSuper = super.shouldRender(villagerFishingBobberEntity, frustum, d, e, f);
        return shouldRenderSuper && hasOwner;
    }

    public void render(VillagerFishingBobberEntityState villagerFishingBobberEntityState, MatrixStack matrixStack, VertexConsumerProvider vertexConsumerProvider, int i) {
        matrixStack.push();
        matrixStack.push();
        matrixStack.scale(0.5F, 0.5F, 0.5F);
        matrixStack.multiply(this.dispatcher.getRotation());
        MatrixStack.Entry entry = matrixStack.peek();
        VertexConsumer vertexConsumer = vertexConsumerProvider.getBuffer(LAYER);
        vertex(vertexConsumer, entry, i, 0.0F, 0.0F, 0, 1);
        vertex(vertexConsumer, entry, i, 1.0F, 0.0F, 1, 1);
        vertex(vertexConsumer, entry, i, 1.0F, 1.0F, 1, 0);
        vertex(vertexConsumer, entry, i, 0.0F, 1.0F, 0, 0);
        matrixStack.pop();
        float f = (float)villagerFishingBobberEntityState.pos.x;
        float g = (float)villagerFishingBobberEntityState.pos.y;
        float h = (float)villagerFishingBobberEntityState.pos.z;
        VertexConsumer vertexConsumer2 = vertexConsumerProvider.getBuffer(RenderLayer.getLineStrip());
        MatrixStack.Entry entry2 = matrixStack.peek();

        for(int k = 0; k <= 16; ++k) {
            renderFishingLine(f, g, h, vertexConsumer2, entry2, percentage(k), percentage(k + 1));
        }

        matrixStack.pop();
        super.render(villagerFishingBobberEntityState, matrixStack, vertexConsumerProvider, i);
    }

    private Vec3d getHandPos(VillagerEntity villager, float ignoredF, float tickProgress) {
        float yaw = MathHelper.lerp(tickProgress, villager.lastBodyYaw, villager.bodyYaw) * ((float)Math.PI / 180F);
        double sin = MathHelper.sin(yaw);
        double cos = MathHelper.cos(yaw);
        float scale = villager.getScale();
        double forwardOffset = 0.8 * scale + 0.1;
        double verticalOffset = -0.45 * scale + 0.22;
        if (villager.isInSneakingPose()) verticalOffset -= 0.1875F;

        return villager.getCameraPosVec(tickProgress).add(-sin * forwardOffset, verticalOffset, cos * forwardOffset);
    }

    private static float percentage(int value) {
        return (float)value / (float) 16;
    }

    private static void vertex(VertexConsumer buffer, MatrixStack.Entry matrix, int light, float x, float y, int u, int v) {
        buffer.vertex(matrix, x - 0.5F, y - 0.5F, 0.0F).color(-1).texture((float)u, (float)v).overlay(OverlayTexture.DEFAULT_UV).light(light).normal(matrix, 0.0F, 1.0F, 0.0F);
    }

    private static void renderFishingLine(float x, float y, float z, VertexConsumer buffer, MatrixStack.Entry matrices, float segmentStart, float segmentEnd) {
        float f = x * segmentStart;
        float g = y * (segmentStart * segmentStart + segmentStart) * 0.5F + 0.25F;
        float h = z * segmentStart;
        float i = x * segmentEnd - f;
        float j = y * (segmentEnd * segmentEnd + segmentEnd) * 0.5F + 0.25F - g;
        float k = z * segmentEnd - h;
        float l = MathHelper.sqrt(i * i + j * j + k * k);
        i /= l;
        j /= l;
        k /= l;
        buffer.vertex(matrices, f, g, h).color(-16777216).normal(matrices, i, j, k);
    }

    public VillagerFishingBobberEntityState createRenderState() {
        return new VillagerFishingBobberEntityState();
    }

    public void updateRenderState(VillagerFishingBobberEntity villagerFishingBobberEntity, VillagerFishingBobberEntityState villagerFishingBobberEntityState, float f) {
        super.updateRenderState(villagerFishingBobberEntity, villagerFishingBobberEntityState, f);
        VillagerEntity villagerEntity = villagerFishingBobberEntity.getBobberOwner();
        if (villagerEntity == null) {
            villagerFishingBobberEntityState.pos = Vec3d.ZERO;
        } else {
            float g = villagerEntity.getHandSwingProgress(f);
            float h = MathHelper.sin(MathHelper.sqrt(g) * (float)Math.PI);
            Vec3d vec3d = this.getHandPos(villagerEntity, h, f);
            Vec3d vec3d2 = villagerFishingBobberEntity.getLerpedPos(f).add(0.0, 0.25, 0.0);
            villagerFishingBobberEntityState.pos = vec3d.subtract(vec3d2);
        }
    }

    protected boolean canBeCulled(VillagerFishingBobberEntity villagerFishingBobberEntity) {
        return false;
    }
}