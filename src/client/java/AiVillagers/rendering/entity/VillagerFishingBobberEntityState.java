package AiVillagers.rendering.entity;

import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.render.entity.state.EntityRenderState;
import net.minecraft.util.math.Vec3d;

@Environment(EnvType.CLIENT)
public class VillagerFishingBobberEntityState extends EntityRenderState {
    public Vec3d pos;

    public VillagerFishingBobberEntityState() {
        this.pos = Vec3d.ZERO;
    }
}
